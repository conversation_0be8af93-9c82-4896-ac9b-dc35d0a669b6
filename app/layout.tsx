import type { Metada<PERSON> } from "next";
import "./globals.css";
import { ClientProviders } from "@/components/client-providers";
import Head from "next/head";

export const metadata: Metadata = {
  title: "Tantan Subscription ",
  description: "Tantan Subscription",
  generator: "Next.js",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <Head>
        <head>
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
          />
        </head>
      </Head>
      <body suppressHydrationWarning>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
