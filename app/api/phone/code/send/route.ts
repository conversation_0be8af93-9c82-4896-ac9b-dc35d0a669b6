import { type NextRequest, NextResponse } from "next/server"

// 后端服务地址（请根据实际情况修改）
const BACKEND_URL = "http://backend.example.com/v1/phone/code/send"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // 转发请求到后端服务
    const backendRes = await fetch(BACKEND_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    })

    const data = await backendRes.json()
    return NextResponse.json(data, { status: backendRes.status })
  } catch (error) {
    console.error("Proxy error for phone code send:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "An unexpected error occurred while proxying verification code request"
      },
      { status: 500 }
    )
  }
} 