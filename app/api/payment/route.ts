import { type NextRequest, NextResponse } from "next/server"
import type { PaymentResponse } from "@/types/payment"

export async function POST(request: NextRequest) {
  try {
    // 检查环境变量
    if (!process.env.CHECKOUT_SECRET_KEY) {
      console.error("CHECKOUT_SECRET_KEY environment variable is not set")
      return NextResponse.json({ error: "Payment configuration error" }, { status: 500 })
    }

    const body = await request.json()
    console.log("Payment request received:", { ...body, token: "***" })

    const { token, amount, currency, reference, customer } = body

    // 验证必需字段
    if (!token || !amount || !currency || !customer?.email) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // 调用 Checkout.com API 处理支付
    const paymentPayload = {
      source: {
        type: "token",
        token: token,
      },
      amount: amount,
      currency: currency,
      reference: reference,
      customer: customer,
      capture: true,
      success_url: `${request.nextUrl.origin}/payment/success`,
      failure_url: `${request.nextUrl.origin}/payment/failure`,
    }

    console.log("Sending payment to Checkout.com:", { ...paymentPayload, source: { type: "token", token: "***" } })

    const paymentResponse = await fetch("https://api.checkout.com/payments", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.CHECKOUT_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(paymentPayload),
    })

    const paymentResult = await paymentResponse.json()
    console.log("Checkout.com response:", paymentResult)

    if (!paymentResponse.ok) {
      console.error("Payment failed:", paymentResult)
      return NextResponse.json(
        {
          error: "Payment failed",
          response_summary: paymentResult.error_type || "Payment processing failed",
          details: paymentResult,
        },
        { status: 400 },
      )
    }

    // 返回支付结果
    const response: PaymentResponse = {
      id: paymentResult.id,
      status: paymentResult.status,
      amount: paymentResult.amount,
      currency: paymentResult.currency,
      reference: paymentResult.reference,
      approved: paymentResult.approved || false,
      response_code: paymentResult.response_code || "",
      response_summary: paymentResult.response_summary || "Payment processed",
    }

    console.log("Payment response:", response)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Payment processing error:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        response_summary: "An unexpected error occurred during payment processing",
      },
      { status: 500 },
    )
  }
}
