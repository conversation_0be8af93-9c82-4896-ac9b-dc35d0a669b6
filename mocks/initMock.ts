export async function initMocks(): Promise<boolean> {
  return new Promise(async (resolve, reject) => {
    try {
      if (typeof window === "undefined") {
        // Node 环境（例如测试或 SSR）
        const { server } = await import("./server");
        await server.listen();
        resolve(true);
      } else {
        // 浏览器环境
        const { worker } = await import("./browser");
        await worker.start({
          onUnhandledRequest: "bypass", // 未处理的请求直接绕过
        });
        resolve(true);
      }
    } catch (error) {
      reject(error);
    }
  });
}
