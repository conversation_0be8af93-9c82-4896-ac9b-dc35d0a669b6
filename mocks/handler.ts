// src/mocks/handlers.ts
import { http, HttpResponse } from "msw";

export const handlers = [
  http.get("/v2/users/me/ttt/checkout-order-status", ({ request }) => {
    console.log("🎯 MSW intercepted checkout-order-status request:", request.url);
    const url = new URL(request.url);
    console.log("📋 Query parameters:", Object.fromEntries(url.searchParams));
    return HttpResponse.json({
      meta: {
        code: 200,
        message: "OK",
      },
      data: {
        itemId: "5206",
        orderStatus: 4,
        orderIdentifier: "1750848403409206034861362",
        platform: "checkout",
      },
    });
  }),
  // http.post("/v1/phone/signin", ({ request }) => {
  //   return HttpResponse.json({
  //     meta: {
  //       code: 200,
  //       message: "OK",
  //     },
  //     data: {
  //       clientId: "100033",
  //       captcha: {
  //         captchaID: "",
  //         captchaOutput: "",
  //         genTime: "",
  //         lotNumber: "",
  //         passToken: "",
  //       },
  //       category: "Text",
  //       codeLength: 4,
  //       language: "zh-CN",
  //       countryCode: 93,
  //       mobileNumber: "1234001",
  //     },
  //   });
  // }),

  // http.post("/v1/phone/code/send", ({ request }) => {
  //   return HttpResponse.json({
  //     meta: {
  //       code: 200,
  //       message: "OK",
  //     },
  //     data: {
  //       clientId: "100033",
  //       captcha: {
  //         captchaID: "",
  //         captchaOutput: "",
  //         genTime: "",
  //         lotNumber: "",
  //         passToken: "",
  //       },

  //       category: "Text",
  //       codeLength: 4,
  //       language: "zh-CN",
  //       countryCode: 93,
  //       mobileNumber: "1234001",
  //     },
  //   });
  // }),

  // http.post("/v1/email/code/send", ({ request }) => {
  //   return HttpResponse.json({
  //     meta: {
  //       code: 200,
  //       message: "OK",
  //     },
  //     data: {
  //       clientId: "100033",
  //       captcha: {
  //         captchaID: "",
  //         captchaOutput: "",
  //         genTime: "",
  //         lotNumber: "",
  //         passToken: "",
  //       },
  //       category: "Text",
  //       codeLength: 4,
  //       language: "zh-CN",
  //       email: "<EMAIL>",
  //     },
  //   });
  // }),

  // http.post("/v1/email/signin", ({ request }) => {
  //   return HttpResponse.json({
  //     meta: {
  //       code: 200,
  //       message: "OK",
  //     },
  //     data: {
  //       clientId: "100033",
  //       code: 7169,
  //       password: "",
  //       email: "<EMAIL>",
  //       signinType: "confirmation_code",
  //       extra: {},
  //     },
  //   });
  // }),

  http.get("/v2/users/me/ttt/checkout-merchandises", ({ request }) => {
    console.log("🎯 MSW intercepted checkout-merchandises request:", request.url);
    return HttpResponse.json({
      meta: {
        code: 200,
        message: "OK",
      },
      data: {
        merchandises: [
          // VIP 商品 - 测试排序：12, 1, 3 -> 应该排序为 1, 3, 12
          {
            id: "tttWebVip:250",
            payment: "checkout",
            category: "tttWebVip",
            quantity: 12,
            productType: "auto-renewable",
            productName: "vip",
            defaultStockKeepUnit: {
              id: 5207,
              prices: {
                currencySymbol: "US$",
                currencyCode: "USD",
                price: "54.99",
                unitPrice: "8.99",
                originalPrice: "0.00",
                originalUnitPrice: "8.99",
                noneRenewalPrice: "0.00",
              },
              extraDisplayOption: {},
            },
          },
          {
            id: "tttWebVip:243",
            payment: "checkout",
            category: "tttWebVip",
            quantity: 1,
            productType: "auto-renewable",
            productName: "vip",
            defaultStockKeepUnit: {
              id: 5205,
              prices: {
                currencySymbol: "US$",
                currencyCode: "USD",
                price: "8.99",
                unitPrice: "8.99",
                originalPrice: "0.00",
                originalUnitPrice: "8.99",
                noneRenewalPrice: "0.00",
              },
              extraDisplayOption: {},
            },
          },
          {
            id: "tttWebVip:244",
            payment: "checkout",
            category: "tttWebVip",
            quantity: 3,
            productType: "auto-renewable",
            productName: "vip",
            defaultStockKeepUnit: {
              id: 5206,
              prices: {
                currencySymbol: "US$",
                currencyCode: "USD",
                price: "16.99",
                unitPrice: "8.99",
                originalPrice: "0.00",
                originalUnitPrice: "8.99",
                noneRenewalPrice: "0.00",
              },
              extraDisplayOption: {},
            },
          },
          // Premium 商品 - 测试排序：1, 12, 3 -> 应该排序为 1, 3, 12
          {
            id: "tttWebPremium:297",
            payment: "checkout",
            category: "tttWebPremium",
            quantity: 1,
            productType: "auto-renewable",
            productName: "svip",
            defaultStockKeepUnit: {
              id: 5211,
              prices: {
                currencySymbol: "US$",
                currencyCode: "USD",
                price: "16.99",
                unitPrice: "16.99",
                originalPrice: "0.00",
                originalUnitPrice: "16.99",
                noneRenewalPrice: "0.00",
              },
              extraDisplayOption: {},
            },
          },
          {
            id: "tttWebPremium:727",
            payment: "checkout",
            category: "tttWebPremium",
            quantity: 12,
            productType: "auto-renewable",
            productName: "svip",
            defaultStockKeepUnit: {
              id: 5213,
              prices: {
                currencySymbol: "US$",
                currencyCode: "USD",
                price: "89.99",
                unitPrice: "16.99",
                originalPrice: "0.00",
                originalUnitPrice: "16.99",
                noneRenewalPrice: "0.00",
              },
              extraDisplayOption: {},
            },
          },
          {
            id: "tttWebPremium:723",
            payment: "checkout",
            category: "tttWebPremium",
            quantity: 3,
            productType: "auto-renewable",
            productName: "svip",
            defaultStockKeepUnit: {
              id: 5212,
              prices: {
                currencySymbol: "US$",
                currencyCode: "USD",
                price: "32.99",
                unitPrice: "16.99",
                originalPrice: "0.00",
                originalUnitPrice: "16.99",
                noneRenewalPrice: "0.00",
              },
              extraDisplayOption: {},
            },
          },
  //           payment: "checkout",
  //           category: "tttWebVip",
  //           quantity: 12,
  //           productType: "auto-renewable",
  //           productName: "vip",
  //           defaultStockKeepUnit: {
  //             id: 5207,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "54.99",
  //               unitPrice: "8.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "8.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebVip:243",
  //           payment: "checkout",
  //           category: "tttWebVip",
  //           quantity: 1,
  //           productType: "auto-renewable",
  //           productName: "vip",
  //           defaultStockKeepUnit: {
  //             id: 5205,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "8.99",
  //               unitPrice: "8.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "8.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebPremium:297",
  //           payment: "checkout",
  //           category: "tttWebPremium",
  //           quantity: 1,
  //           productType: "auto-renewable",
  //           productName: "svip",
  //           defaultStockKeepUnit: {
  //             id: 5211,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "16.99",
  //               unitPrice: "16.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "16.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebPremium:723",
  //           payment: "checkout",
  //           category: "tttWebPremium",
  //           quantity: 3,
  //           productType: "auto-renewable",
  //           productName: "svip",
  //           defaultStockKeepUnit: {
  //             id: 5212,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "32.99",
  //               unitPrice: "16.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "16.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebPremium:727",
  //           payment: "checkout",
  //           category: "tttWebPremium",
  //           quantity: 12,
  //           productType: "auto-renewable",
  //           productName: "svip",
  //           defaultStockKeepUnit: {
  //             id: 5213,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "89.99",
  //               unitPrice: "16.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "16.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebUltraPremium:4365",
  //           payment: "checkout",
  //           category: "tttWebUltraPremium",
  //           quantity: 1,
  //           productType: "auto-renewable",
  //           productName: "ultraPremium",
  //           defaultStockKeepUnit: {
  //             id: 5214,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "103.99",
  //               unitPrice: "103.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "103.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebUltraPremium:4366",
  //           payment: "checkout",
  //           category: "tttWebUltraPremium",
  //           quantity: 3,
  //           productType: "auto-renewable",
  //           productName: "ultraPremium",
  //           defaultStockKeepUnit: {
  //             id: 5215,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "259.99",
  //               unitPrice: "103.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "103.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebUltraPremium:4368",
  //           payment: "checkout",
  //           category: "tttWebUltraPremium",
  //           quantity: 12,
  //           productType: "auto-renewable",
  //           productName: "ultraPremium",
  //           defaultStockKeepUnit: {
  //             id: 5216,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "759.99",
  //               unitPrice: "103.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "103.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebSee:252",
  //           payment: "checkout",
  //           category: "tttWebSee",
  //           quantity: 1,
  //           productType: "auto-renewable",
  //           productName: "seeWhoLikedMe",
  //           defaultStockKeepUnit: {
  //             id: 5208,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "12.99",
  //               unitPrice: "12.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "12.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebSee:254",
  //           payment: "checkout",
  //           category: "tttWebSee",
  //           quantity: 3,
  //           productType: "auto-renewable",
  //           productName: "seeWhoLikedMe",
  //           defaultStockKeepUnit: {
  //             id: 5209,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "24.99",
  //               unitPrice: "12.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "12.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebSee:256",
  //           payment: "checkout",
  //           category: "tttWebSee",
  //           quantity: 12,
  //           productType: "auto-renewable",
  //           productName: "seeWhoLikedMe",
  //           defaultStockKeepUnit: {
  //             id: 5210,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "69.99",
  //               unitPrice: "12.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "12.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
        ],
      },
    });
  }),

  // http.post("/v2/users/me/ttt/checkout-order", ({ request }) => {
  //   return HttpResponse.json({
  //     meta: {
  //       code: 200,
  //       message: "OK",
  //     },
  //     data: {
  //       merchandises: [
  //         {
  //           id: "tttWebVip:244",
  //           payment: "checkout",
  //           category: "tttWebVip",
  //           quantity: 3,
  //           productType: "auto-renewable",
  //           productName: "vip",
  //           defaultStockKeepUnit: {
  //             id: 5206,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "16.99",
  //               unitPrice: "8.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "8.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebVip:250",
  //           payment: "checkout",
  //           category: "tttWebVip",
  //           quantity: 12,
  //           productType: "auto-renewable",
  //           productName: "vip",
  //           defaultStockKeepUnit: {
  //             id: 5207,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "54.99",
  //               unitPrice: "8.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "8.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebVip:243",
  //           payment: "checkout",
  //           category: "tttWebVip",
  //           quantity: 1,
  //           productType: "auto-renewable",
  //           productName: "vip",
  //           defaultStockKeepUnit: {
  //             id: 5205,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "8.99",
  //               unitPrice: "8.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "8.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebPremium:297",
  //           payment: "checkout",
  //           category: "tttWebPremium",
  //           quantity: 1,
  //           productType: "auto-renewable",
  //           productName: "svip",
  //           defaultStockKeepUnit: {
  //             id: 5211,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "16.99",
  //               unitPrice: "16.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "16.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebPremium:723",
  //           payment: "checkout",
  //           category: "tttWebPremium",
  //           quantity: 3,
  //           productType: "auto-renewable",
  //           productName: "svip",
  //           defaultStockKeepUnit: {
  //             id: 5212,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "32.99",
  //               unitPrice: "16.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "16.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebPremium:727",
  //           payment: "checkout",
  //           category: "tttWebPremium",
  //           quantity: 12,
  //           productType: "auto-renewable",
  //           productName: "svip",
  //           defaultStockKeepUnit: {
  //             id: 5213,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "89.99",
  //               unitPrice: "16.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "16.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebUltraPremium:4365",
  //           payment: "checkout",
  //           category: "tttWebUltraPremium",
  //           quantity: 1,
  //           productType: "auto-renewable",
  //           productName: "ultraPremium",
  //           defaultStockKeepUnit: {
  //             id: 5214,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "103.99",
  //               unitPrice: "103.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "103.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebUltraPremium:4366",
  //           payment: "checkout",
  //           category: "tttWebUltraPremium",
  //           quantity: 3,
  //           productType: "auto-renewable",
  //           productName: "ultraPremium",
  //           defaultStockKeepUnit: {
  //             id: 5215,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "259.99",
  //               unitPrice: "103.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "103.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebUltraPremium:4368",
  //           payment: "checkout",
  //           category: "tttWebUltraPremium",
  //           quantity: 12,
  //           productType: "auto-renewable",
  //           productName: "ultraPremium",
  //           defaultStockKeepUnit: {
  //             id: 5216,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "759.99",
  //               unitPrice: "103.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "103.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebSee:252",
  //           payment: "checkout",
  //           category: "tttWebSee",
  //           quantity: 1,
  //           productType: "auto-renewable",
  //           productName: "seeWhoLikedMe",
  //           defaultStockKeepUnit: {
  //             id: 5208,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "12.99",
  //               unitPrice: "12.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "12.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebSee:254",
  //           payment: "checkout",
  //           category: "tttWebSee",
  //           quantity: 3,
  //           productType: "auto-renewable",
  //           productName: "seeWhoLikedMe",
  //           defaultStockKeepUnit: {
  //             id: 5209,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "24.99",
  //               unitPrice: "12.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "12.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //         {
  //           id: "tttWebSee:256",
  //           payment: "checkout",
  //           category: "tttWebSee",
  //           quantity: 12,
  //           productType: "auto-renewable",
  //           productName: "seeWhoLikedMe",
  //           defaultStockKeepUnit: {
  //             id: 5210,
  //             prices: {
  //               currencySymbol: "US$",
  //               currencyCode: "USD",
  //               price: "69.99",
  //               unitPrice: "12.99",
  //               originalPrice: "0.00",
  //               originalUnitPrice: "12.99",
  //               noneRenewalPrice: "0.00",
  //             },
  //             extraDisplayOption: {},
  //           },
  //         },
  //       ],
  //     },
  //   });
  // }),
];
