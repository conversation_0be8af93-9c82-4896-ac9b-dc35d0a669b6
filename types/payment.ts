export interface PaymentRequest {
  amount: number;
  currency: string;
  reference: string;
  customer: {
    email: string;
    name: string;
  };
}

export interface PaymentResponse {
  id: string;
  status: string;
  amount: number;
  currency: string;
  reference: string;
  approved: boolean;
  response_code: string;
  response_summary: string;
}

export interface FramesCardTokenizedEvent {
  type: "CARD_TOKENIZED";
  token: string;
  scheme: string;
  last4: string;
  bin: string;
  card_type: string;
  issuer_country: string;
  product_id: string;
  product_type: string;
}

export interface DefaultStockKeepUnitPrices {
  currencySymbol: string;
  currencyCode: string;
  price: string;
  unitPrice: string;
  originalPrice: string;
  originalUnitPrice: string;
  noneRenewalPrice: string;
}

export interface DefaultStockKeepUnit {
  id: number;
  prices: DefaultStockKeepUnitPrices;
  extraDisplayOption: { [key: string]: any }; // 可以是任意其他属性
}

export interface Plan {
  id: string;
  payment: string;
  category: string;
  quantity: number;
  productType: string;
  productName: string;
  defaultStockKeepUnit: DefaultStockKeepUnit;
  tantanCoinBonus: number;
  name: string;
  icon: string;
  categoryName: string;
  discount?: string;
}

export interface ShopItem {
  type: string;
  fold: boolean;
  title: string;
  plans: Plan[];
  defaultItem?: number;
}

export type Shops = ShopItem[];
