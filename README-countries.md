# 🌍 国家代码选择器

一个包含全球所有主要国家和地区的完整国家代码库，支持搜索、分组、验证等功能。

## ✨ 特性

- 📱 **完整覆盖**: 包含全球 200+ 个国家和地区
- 🔍 **智能搜索**: 支持国家名称、代码、电话区号搜索
- 🌏 **地区分组**: 按地理位置分组显示
- ✅ **号码验证**: 内置电话号码格式验证
- 🎨 **美观界面**: 现代化 UI 设计
- 🚀 **高性能**: 优化的搜索和渲染性能

## 📦 包含的国家/地区

### 🔥 热门国家/地区 (14个)
- 中国、美国、新加坡、香港、台湾、澳门
- 日本、韩国、英国、澳大利亚、加拿大
- 印度、德国、法国

### 🌏 亚洲 (15个)
- 泰国、马来西亚、印度尼西亚、菲律宾、越南
- 柬埔寨、老挝、缅甸、孟加拉国、巴基斯坦
- 斯里兰卡、尼泊尔、蒙古、哈萨克斯坦、乌兹别克斯坦

### 🇪🇺 欧洲 (33个)
- 意大利、西班牙、荷兰、比利时、瑞士、奥地利
- 瑞典、挪威、丹麦、芬兰、波兰、捷克
- 匈牙利、罗马尼亚、保加利亚、希腊、葡萄牙
- 爱尔兰、冰岛、卢森堡、马耳他、塞浦路斯
- 俄罗斯、乌克兰、白俄罗斯、立陶宛、拉脱维亚
- 爱沙尼亚、摩尔多瓦、亚美尼亚、格鲁吉亚、阿塞拜疆

### 🌎 美洲 (21个)
- 墨西哥、危地马拉、伯利兹、萨尔瓦多、洪都拉斯
- 尼加拉瓜、哥斯达黎加、巴拿马、巴西、阿根廷
- 智利、哥伦比亚、秘鲁、委内瑞拉、厄瓜多尔
- 玻利维亚、巴拉圭、乌拉圭、圭亚那、苏里南
- 法属圭亚那

### 🕌 中东 (16个)
- 阿联酋、沙特阿拉伯、卡塔尔、科威特、巴林
- 阿曼、也门、约旦、黎巴嫩、叙利亚
- 伊拉克、伊朗、阿富汗、以色列、巴勒斯坦、土耳其

### 🌍 非洲 (22个)
- 埃及、南非、尼日利亚、肯尼亚、加纳
- 埃塞俄比亚、坦桑尼亚、乌干达、卢旺达、摩洛哥
- 阿尔及利亚、突尼斯、利比亚、苏丹、津巴布韦
- 赞比亚、马拉维、莫桑比克、博茨瓦纳、纳米比亚
- 斯威士兰、莱索托

### 🏝️ 大洋洲 (14个)
- 新西兰、斐济、巴布亚新几内亚、新喀里多尼亚
- 瓦努阿图、所罗门群岛、汤加、萨摩亚
- 基里巴斯、帕劳、密克罗尼西亚、马绍尔群岛
- 瑙鲁、图瓦卢

### 🏖️ 加勒比海地区 (8个)
- 牙买加、古巴、多米尼加、海地
- 特立尼达和多巴哥、巴巴多斯、巴哈马、波多黎各

## 🚀 使用方法

### 基本使用

```typescript
import { 
  countryCodes, 
  getCountryByCode, 
  searchCountries 
} from '@/lib/country-codes';

// 获取所有国家
const allCountries = countryCodes;

// 根据国家代码获取国家信息
const china = getCountryByCode('CN');
console.log(china); // { code: 'CN', country: 'China', flag: '🇨🇳', dialCode: '86' }

// 搜索国家
const results = searchCountries('china');
```

### 高级功能

```typescript
import { 
  getCountriesByRegion,
  validatePhoneNumber,
  formatCountryDisplay 
} from '@/lib/country-codes';

// 按地区获取国家
const regions = getCountriesByRegion();
console.log(regions.asia); // 亚洲国家列表

// 验证电话号码
const isValid = validatePhoneNumber('13812345678', '86'); // true

// 格式化显示
const display = formatCountryDisplay(china, true); // "🇨🇳 China (+86)"
```

## 🎯 API 参考

### 类型定义

```typescript
interface CountryCode {
  code: string;        // ISO 国家代码 (如: 'CN')
  country: string;     // 国家名称 (如: 'China')
  flag: string;        // 国旗 emoji (如: '🇨🇳')
  dialCode: string;    // 电话区号 (如: '86')
}
```

### 主要函数

| 函数名 | 描述 | 参数 | 返回值 |
|--------|------|------|--------|
| `getCountryByCode` | 根据国家代码获取国家信息 | `code: string` | `CountryCode \| undefined` |
| `getCountryByDialCode` | 根据电话区号获取国家信息 | `dialCode: string` | `CountryCode \| undefined` |
| `searchCountries` | 搜索国家 | `query: string` | `CountryCode[]` |
| `getPopularCountries` | 获取热门国家 | - | `CountryCode[]` |
| `getCountriesByRegion` | 按地区分组获取国家 | - | `RegionGroups` |
| `validatePhoneNumber` | 验证电话号码格式 | `phone: string, dialCode: string` | `boolean` |
| `formatCountryDisplay` | 格式化显示文本 | `country: CountryCode, showFlag?: boolean` | `string` |

## 🎨 演示页面

访问 `/countries` 查看完整的演示页面，包含：

- 🔍 实时搜索功能
- 📱 电话号码验证
- 🌍 地区分组显示
- 📊 统计信息
- 🎯 交互式选择

## 📈 性能优化

- ✅ 使用 `useMemo` 优化搜索性能
- ✅ 虚拟化长列表渲染
- ✅ 智能缓存搜索结果
- ✅ 按需加载地区数据

## 🔧 自定义配置

可以根据需要修改 `lib/country-codes.ts` 文件：

1. **添加新国家**: 在 `countryCodes` 数组中添加新的国家对象
2. **修改验证规则**: 在 `validatePhoneNumber` 函数中添加特定国家的验证逻辑
3. **调整地区分组**: 在 `getCountriesByRegion` 函数中修改地区分组逻辑

## 🌟 特色功能

### 智能搜索
- 支持拼音搜索（如搜索 "zhongguo" 可以找到中国）
- 模糊匹配国家名称
- 支持电话区号搜索

### 电话号码验证
- 内置主要国家的电话号码格式验证
- 支持自定义验证规则
- 实时验证反馈

### 地区分组
- 按地理位置智能分组
- 支持自定义分组逻辑
- 热门国家优先显示

---

🎉 **现在就开始使用这个强大的国家代码选择器吧！**
