import type { Config } from "tailwindcss";
import {
  generateSafelist,
  generateTailwindColors,
  themeColors,
} from "./lib/themeColor";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        ...generateTailwindColors(),
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  safelist: [
    // 确保所有动态类名都被包含
    "bg-gradient-to-br",
    "from-amber-50",
    "to-orange-100",
    "from-gray-900",
    "to-black",
    "from-amber-900",
    "to-yellow-900",
    "bg-white/80",
    "bg-gray-800/80",
    "bg-amber-100/20",
    "bg-gradient-to-b",
    "from-yellow-300",
    "to-amber-400",
    "from-purple-400",
    "to-pink-400",
    "from-yellow-400",
    "to-amber-500",
    "text-black",
    "text-white",
    "text-gray-900",
    "text-gray-600",
    "text-gray-300",
    "text-amber-200",
    "text-amber-600",
    "text-purple-400",
    "text-yellow-400",
    "hover:text-gray-900",
    "hover:text-white",
    "scale-105",
    "scale-102",
    "hover:scale-102",
    "bg-[#FFDC7A]",
    "bg-[#fff7ee]",
    "bg-gradient-to-b from-[#FFEFC0] via-[#FFF3D0] to-[#FFFAED]",
    "bg-gradient-to-b from-[#FFE2BF] via-[#FFF4E8] to-[#FFFBF7]",
    "bg-gradient-to-b from-[#493f31]  to-[#141211]",
    "bg-gradient-to-b from-[#FFE391] to-[#FFFFFF]",
    "bg-[linear-gradient(-45deg,#fed998_0%,#fdecb9_50%,#fed998_100%)]",
    ...Object.values(generateSafelist()),
  ],
  plugins: [require("tailwindcss-animate")],
};
export default config;
