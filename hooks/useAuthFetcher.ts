'use client'

import { useRouter } from 'next/navigation'
import { useCallback } from 'react'
import { fetcher } from '@/lib/fetcher'

interface FetcherOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE";
  body?: any;
  headers?: Record<string, string>;
  needAuth?: boolean;
}

export function useAuthFetcher() {
  const router = useRouter()

  const authFetcher = useCallback(async <T>(
    url: string,
    options: FetcherOptions = {}
  ): Promise<T> => {
    try {
      return await fetcher<T>(url, options)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      // 检查是否是认证相关错误
      if (
        errorMessage.includes('401') || 
        errorMessage.includes('Unauthorized') ||
        errorMessage.includes('token') ||
        errorMessage.includes('authentication')
      ) {
        console.log('Token invalid, redirecting to login...')
        
        // 清除本地存储的token
        if (typeof window !== 'undefined') {
          localStorage.removeItem('token')
          localStorage.removeItem('refreshToken')
        }
        
        // 跳转到登录页面
        // router.push('/login')
        
        throw new Error('Authentication failed, redirected to login')
      }
      
      // 重新抛出其他错误
      throw error
    }
  }, [router])

  return authFetcher
}
