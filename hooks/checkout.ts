import { QueryClient, useMutation } from "@tanstack/react-query";
import {
  checkoutOrder,
  checkoutMerchandises,
  checkoutCardsInfo,
  checkoutOrderStatus,
  checkoutContractList,
  checkoutContractCancel,
} from "@/server/checkout";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2, // 失败重试2次
      staleTime: 1000 * 60 * 5, // 数据缓存5分钟
    },
    mutations: {
      onError: (error) => {
        console.error("Mutation error:", error.message);
        // 可添加 Toast 通知或日志上报
      },
    },
  },
});

export const useCheckoutOrder = () => {
  return useMutation({
    mutationFn: checkoutOrder,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};

export const useCheckoutMerchandises = () => {
  return useMutation({
    mutationFn: checkoutMerchandises,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutCardsInfo = () => {
  return useMutation({
    mutationFn: checkoutCardsInfo,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutOrderStatus = () => {
  return useMutation({
    mutationFn: checkoutOrderStatus,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutContractList = () => {
  return useMutation({
    mutationFn: checkoutContractList,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutContractCancel = () => {
  return useMutation({
    mutationFn: checkoutContractCancel,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
