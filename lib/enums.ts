export const textUrlMap = {
  vip: {
    selected:
      "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_text-selected.png",
    unSelected: {
      light:
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_text_light_unselected.png",
      dark: "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_text_dark_unselected.png",
    },
    width: 35,
    height: 22,
  },
  see: {
    selected:
      "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/see_text_selected.png",
    unSelected: {
      light:
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/see_text_light_unselected.png",
      dark: "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/see_text_dark_unselected.png",
    },
    width: 43,
    height: 26,
  },
  premium: {
    selected:
      "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/premium_text_selected.png",
    unSelected: {
      light:
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/premium_text_light_unselected.png",
      dark: "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/premium_text_dark_unselected.png",
    },
    width: 99,
    height: 22,
  },
  ultra: {
    selected:
      "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/ultra_text_selected.png",
    unSelected: {
      light:
        "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/ultra_text_light_unselected.png",
      dark: "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/ultra_text_dark_unselected.png",
    },
    width: 103.33,
    height: 30,
  },
};

/** checkout 商品列表接口productName 对应表 */
export const TRANSFORM_PRODUCT_NAME: any = {
  vip: "vip",
  seeWhoLikedMe: "see",
  svip: "premium",
  ultraPremium: "ultra",
};

export const plans = [
  {
    id: "1",
    title: "VIP",
    months: 12,
    monthlyPrice: "$51.5/mo",
    totalPrice: "$618",
    discount: "16%off",
  },
  {
    id: "2",
    title: "SEE",
    months: 3,
    monthlyPrice: "$32.5/mo",
    totalPrice: "$128",
  },
  {
    id: "3",
    title: "PREMIUM",
    months: 3,
    monthlyPrice: "$32.5/mo",
    totalPrice: "$128",
  },
];

export const shopList = [
  {
    type: "vip",
    fold: false,
    items: [
      {
        id: 5205,
        payment: "checkout",
        category: "tttWebVip",
        quantity: 1,
        productType: "auto-renewable",
        productName: "vip",
        defaultStockKeepUnit: {
          id: 5205,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "8.99",
            unitPrice: "8.99",
            originalPrice: "0.00",
            originalUnitPrice: "8.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "vip",
        categoryName: "vip",
      },
      {
        id: 5206,
        payment: "checkout",
        category: "tttWebVip",
        quantity: 3,
        productType: "auto-renewable",
        productName: "vip",
        defaultStockKeepUnit: {
          id: 5206,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "16.99",
            unitPrice: "8.99",
            originalPrice: "0.00",
            originalUnitPrice: "8.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "vip",
        categoryName: "vip",
      },
      {
        id: 5207,
        payment: "checkout",
        category: "tttWebVip",
        quantity: 12,
        productType: "auto-renewable",
        productName: "vip",
        defaultStockKeepUnit: {
          id: 5207,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "54.99",
            unitPrice: "8.99",
            originalPrice: "0.00",
            originalUnitPrice: "8.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "vip",
        categoryName: "vip",
      },
    ],
  },
  {
    type: "see",
    fold: false,
    items: [
      {
        id: 5208,
        payment: "checkout",
        category: "tttWebSee",
        quantity: 1,
        productType: "auto-renewable",
        productName: "seeWhoLikedMe",
        defaultStockKeepUnit: {
          id: 5208,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "12.99",
            unitPrice: "12.99",
            originalPrice: "0.00",
            originalUnitPrice: "12.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "see",
        categoryName: "see",
      },
      {
        id: 5209,
        payment: "checkout",
        category: "tttWebSee",
        quantity: 3,
        productType: "auto-renewable",
        productName: "seeWhoLikedMe",
        defaultStockKeepUnit: {
          id: 5209,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "24.99",
            unitPrice: "12.99",
            originalPrice: "0.00",
            originalUnitPrice: "12.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "see",
        categoryName: "see",
      },
      {
        id: 5210,
        payment: "checkout",
        category: "tttWebSee",
        quantity: 12,
        productType: "auto-renewable",
        productName: "seeWhoLikedMe",
        defaultStockKeepUnit: {
          id: 5210,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "69.99",
            unitPrice: "12.99",
            originalPrice: "0.00",
            originalUnitPrice: "12.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "see",
        categoryName: "see",
      },
    ],
  },
  {
    type: "premium",
    fold: false,
    items: [
      {
        id: 5211,
        payment: "checkout",
        category: "tttWebPremium",
        quantity: 1,
        productType: "auto-renewable",
        productName: "svip",
        defaultStockKeepUnit: {
          id: 5211,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "16.99",
            unitPrice: "16.99",
            originalPrice: "0.00",
            originalUnitPrice: "16.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "premium",
        categoryName: "premium",
      },
      {
        id: 5212,
        payment: "checkout",
        category: "tttWebPremium",
        quantity: 3,
        productType: "auto-renewable",
        productName: "svip",
        defaultStockKeepUnit: {
          id: 5212,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "32.99",
            unitPrice: "16.99",
            originalPrice: "0.00",
            originalUnitPrice: "16.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "premium",
        categoryName: "premium",
      },
      {
        id: 5213,
        payment: "checkout",
        category: "tttWebPremium",
        quantity: 12,
        productType: "auto-renewable",
        productName: "svip",
        defaultStockKeepUnit: {
          id: 5213,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "89.99",
            unitPrice: "16.99",
            originalPrice: "0.00",
            originalUnitPrice: "16.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "premium",
        categoryName: "premium",
      },
    ],
  },
  {
    type: "ultra",
    fold: false,
    items: [
      {
        id: 5214,
        payment: "checkout",
        category: "tttWebUltraPremium",
        quantity: 1,
        productType: "auto-renewable",
        productName: "ultraPremium",
        defaultStockKeepUnit: {
          id: 5214,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "103.99",
            unitPrice: "103.99",
            originalPrice: "0.00",
            originalUnitPrice: "103.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "ultra",
        categoryName: "ultra",
      },
      {
        id: 5215,
        payment: "checkout",
        category: "tttWebUltraPremium",
        quantity: 3,
        productType: "auto-renewable",
        productName: "ultraPremium",
        defaultStockKeepUnit: {
          id: 5215,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "259.99",
            unitPrice: "103.99",
            originalPrice: "0.00",
            originalUnitPrice: "103.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "ultra",
        categoryName: "ultra",
      },
      {
        id: 5216,
        payment: "checkout",
        category: "tttWebUltraPremium",
        quantity: 12,
        productType: "auto-renewable",
        productName: "ultraPremium",
        defaultStockKeepUnit: {
          id: 5216,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "759.99",
            unitPrice: "103.99",
            originalPrice: "0.00",
            originalUnitPrice: "103.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "ultra",
        categoryName: "ultra",
      },
    ],
  },
];

export const tabs = [
  { type: "vip", label: "VIP" },
  { type: "see", label: "SEE" },
  { type: "premium", label: "PREMIUM" },
  { type: "ultra", label: "ULTRA PREMIUM" },
];

export const CATEGORY_NAME = {
  vip: "vip",
  see: "see",
  svip: "premium",
  oDiamond: "ultra",
};
