import sha1 from "crypto-js/sha1";
import sha256 from "crypto-js/sha256";
import sha512 from "crypto-js/sha512";
import Base64 from "crypto-js/enc-base64";
import { v4 as uuid } from "uuid";
import * as CryptoJS from "crypto-js";

export const urlParser = (url: string) => {
  if (!url) return {};
  let tmp = <any>[];
  let result = <any>{};

  // Ignore Hashbangs.
  if ((tmp = url.match(/(.*?)\/#\!(.*)/))) {
    url = tmp[1] + tmp[2];
  }

  // Hash.
  if ((tmp = url.match(/(.*?)#(.*)/))) {
    result.hash = tmp[2];
    url = tmp[1];
  }

  // Query
  if ((tmp = url.match(/(.*?)\?(.*)/))) {
    result.query = tmp[2];
    url = tmp[1];
  }

  // Protocol.
  if ((tmp = url.match(/(.*?)\:?\/\/(.*)/))) {
    result.protocol = tmp[1].toLowerCase();
    url = tmp[2];
  }

  // Path.
  if ((tmp = url.match(/(.*?)(\/.*)/))) {
    result.path = tmp[2];
    url = tmp[1];
  }

  // Clean up path.
  result.path = (result.path || "").replace(/^([^\/])/, "/$1");

  // Port.
  if ((tmp = url.match(/(.*)\:([0-9]+)$/))) {
    result.port = tmp[2];
    url = tmp[1];
  }

  // Auth.
  if ((tmp = url.match(/(.*?)@(.*)/))) {
    result.auth = tmp[1];
    url = tmp[2];
  }

  // User and pass.
  if (result.auth) {
    tmp = result.auth.match(/(.*)\:(.*)/);

    result.user = tmp ? tmp[1] : result.auth;
    result.pass = tmp ? tmp[2] : undefined;
  }

  // Hostname.
  result.hostname = url.toLowerCase();

  if (result.query) {
    let params = <any>{};
    result.query.split("&").map((item) => {
      let [key, val] = item.split("=");
      params[key] = val || "";
    });
    result.params = params;
  }
  return result;
};

urlParser("http://www.baidu.com/a/b/c?a=1&b=2#123");

/**
 * string 转 Uint8Array
 */
export const string2Uint8Array = (str: string): Uint8Array => {
  const buffer = new ArrayBuffer(str.length);
  const view = new Uint8Array(buffer);
  for (let i = 0; i < str.length; i++) {
    view[i] = str.charCodeAt(i);
  }
  return view;
};

//wordArray 转 Uint8Array
const wordArrayToUint8Array = (
  wordArray: CryptoJS.lib.WordArray
): Uint8Array => {
  const words = wordArray.words;
  const sigBytes = wordArray.sigBytes;
  const u8 = new Uint8Array(sigBytes);
  for (let i = 0; i < sigBytes; i++) {
    u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
  }
  return u8;
};

/**
 * 签名密钥 base64
 */
const secret_string = process.env.HMAC_SECRET_STRING;

/**
 * 签名密钥 Uint8Array
 */
// const secret_uint8Array: Uint8Array = new Uint8Array(secret_string);
const secret_uint8Array: Uint8Array = string2Uint8Array(secret_string || "");

/**
 * HMAC2鉴权
 * 新版 HMAC-V2 哈希签名算法设计文档：https://alidocs.dingtalk.com/i/nodes/XPwkYGxZV3RPdpXpC72y14e1WAgozOKL
 * 登录1 版本2算法 未登录2 版本3算法
 */
export const getHmacVersion2 = async (url: string, body = "") => {
  const token = getToken() || "";
  console.log("token", token);
  if (token) {
    return getHmacVersion2_1(url, body, token);
  } else {
    return getHmacVersion2_2(url, body);
  }
};

/**
 * 登录情况下HMAC2鉴权
 */
const getHmacVersion2_1 = (url: string, body = "", token: string) => {
  // 混淆秘钥
  const secret = obfuscateSecret();

  // 生成时间戳
  const timestampMS = generateTimestampMS();

  // message
  const message = generateMessage(timestampMS, token, url, body);

  // 预签名message
  const preSigned = wordArrayToUint8Array(sha256(message));

  // 合并秘钥和预签名
  const secret_preSigned = mergeUint8Array(secret, preSigned);

  // sha1
  //   const result = sha1.update(secret_preSigned).array();
  //   const resultString = Base64.fromUint8Array(result);

  // CryptoJS 的 hasher.update 只接受 string 或 WordArray 类型，不能直接传 Uint8Array
  // 需要将 Uint8Array 转换为 CryptoJS 的 WordArray
  const wordArray = CryptoJS.lib.WordArray.create(secret_preSigned as any);
  const hasher = CryptoJS.algo.SHA1.create();
  hasher.update(wordArray);
  const hash = hasher.finalize();
  const resultString = CryptoJS.enc.Base64.stringify(hash);

  // 拼接
  return `MAC2 ["1","web1.0.0","${timestampMS}","${token}","${resultString}"]`;
};

/**
 * 未登录情况下HMAC2鉴权
 */
const getHmacVersion2_2 = async (url: string, body = "") => {
  // uuid
  const deviceId = await uuid();

  // 混淆秘钥
  const secret = obfuscateSwap();

  // 生成时间戳
  const timestampMS = generateTimestampMS();

  // message
  const message = generateMessage(timestampMS, deviceId, url, body);

  // 预签名message
  const preSigned = wordArrayToUint8Array(sha512(message));

  // 合并秘钥和预签名

  const secret_preSigned = mergeUint8Array(secret, preSigned);

  // sha1
  //   const result = sha1.update(secret_preSigned).array();
  //   const resultString = Base64.fromUint8Array(result);

  const wordArray = CryptoJS.lib.WordArray.create(secret_preSigned as any);
  const hasher = CryptoJS.algo.SHA1.create();
  hasher.update(wordArray);
  const hash = hasher.finalize();
  const resultString = CryptoJS.enc.Base64.stringify(hash);

  // 拼接
  return `MAC2 ["2","web1.0.0","${timestampMS}","${deviceId}","${resultString}"]`;
};

/**
 * 生成虚假时间戳 timestampMS
 */
export const generateTimestampMS = (): string => {
  let ms = new Date().getTime();
  const v = (ms % 1000000) % 177;
  ms += 127 - v;
  return ms.toString();
};

/**
 * 生成query value
 */
export const generateQueryValue = (query: string): string => {
  let result: string = "";
  if (!query) {
    return result;
  }

  let arr = <any>[];
  query.split("&")?.forEach((item) => {
    const [k, v] = item.split("=");
    arr.push({ key: k, value: k === "since" ? decodeURIComponent(v) : v });
  });
  arr = arr?.sort(alphabeticalSort)?.map((item) => item?.value);
  result = arr?.join("$");
  return result;
};

/**
 * 排序对比方法
 */
function alphabeticalSort(a: any, b: any): number {
  return a.key.localeCompare(b.key);
}

/**
 * 生成message
 * message = timestampms|accessToken2|urlPath|queryValue|body
 */
export const generateMessage = (
  timestampMS: string,
  token: string,
  url: string,
  body: string
) => {
  const path = urlParser(url).path || "/";
  const queryValue = generateQueryValue(urlParser(url).query);
  const result = `${timestampMS}|${token}|${path}|${queryValue}|${body}`;
  return result;
};

/**
 * 获取token，没有token时使用uuid
 */
export const getToken = () => {
  try {
    // const token = localStorage.getItem("auth_token");
    const token = '';
    return token;
  } catch (error) {
    console.log(error);
    return "";
  }
};

/**
 * 混淆秘钥，字节移位，每个字节往后移三位
 */
const obfuscateSecret = (): Uint8Array => {
  const result = secret_uint8Array.map((item: number) => {
    return item + 3;
  });
  return result;
};

/**
 * 混淆秘钥，字节交换，交换相邻的两个字节
 */
const obfuscateSwap = (): Uint8Array => {
  const result = new Uint8Array(secret_uint8Array.length);
  for (let i = 0; i < secret_uint8Array.length - 1; i += 2) {
    result[i] = secret_uint8Array[i + 1];
    result[i + 1] = secret_uint8Array[i];
  }
  return result;
};

/**
 * 合并Uint8Array
 */
const mergeUint8Array = (value1: Uint8Array, value2: Uint8Array) => {
  const result = new Uint8Array(value1.length + value2.length);
  result.set(value1);
  result.set(value2, value1.length);
  return result;
};
