/**
 * Checkout.com Frames 脚本加载和管理工具
 */

export interface FramesConfig {
  publicKey: string;
  style?: {
    base?: {
      color?: string;
      fontSize?: string;
      paddingLeft?: string;
      fontWeight?: string;
    };
    placeholder?: {
      base?: {
        color?: string;
      };
    };
    focus?: {
      border?: string;
      borderRadius?: string;
    };
  };
}

export interface FramesLoadOptions {
  maxRetries?: number;
  timeout?: number;
  scriptUrl?: string;
}

/**
 * 检查 Checkout.com Frames 是否已加载
 */
export const isFramesLoaded = (): boolean => {
  return typeof window !== 'undefined' && !!window.Frames;
};

/**
 * 加载 Checkout.com 脚本（单次尝试）
 */
const loadCheckoutScriptOnce = (options: FramesLoadOptions = {}): Promise<void> => {
  const {
    timeout = 10000,
    scriptUrl = "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/framesv2.min.js"
  } = options;

  return new Promise((resolve, reject) => {
    // 检查脚本是否已经加载
    if (isFramesLoaded()) {
      console.log("Checkout.com Frames 已加载");
      resolve();
      return;
    }

    // 检查是否已经有脚本标签在加载中
    const existingScript = document.getElementById("checkout-frames-script");
    if (existingScript) {
      // 如果脚本正在加载，等待加载完成
      existingScript.addEventListener("load", () => resolve());
      existingScript.addEventListener("error", () => reject(new Error("Checkout.com 脚本加载失败")));
      return;
    }

    console.log("开始加载 Checkout.com 脚本...");
    const script = document.createElement("script");
    script.src = scriptUrl;
    script.id = "checkout-frames-script";
    script.async = true;
    
    // 设置超时
    const timeoutId = setTimeout(() => {
      script.remove();
      reject(new Error(`Checkout.com 脚本加载超时 (${timeout}ms)`));
    }, timeout);
    
    script.onload = () => {
      clearTimeout(timeoutId);
      console.log("Checkout.com 脚本加载成功");
      
      // 等待一小段时间确保 Frames 对象完全初始化
      setTimeout(() => {
        if (isFramesLoaded()) {
          resolve();
        } else {
          reject(new Error("Checkout.com Frames 对象未找到"));
        }
      }, 200);
    };
    
    script.onerror = (error) => {
      clearTimeout(timeoutId);
      console.error("Checkout.com 脚本加载失败:", error);
      script.remove();
      reject(new Error("Checkout.com 脚本加载失败"));
    };
    
    document.head.appendChild(script);
  });
};

/**
 * 加载 Checkout.com 脚本（带重试机制）
 */
export const loadCheckoutScript = async (options: FramesLoadOptions = {}): Promise<void> => {
  const { maxRetries = 3 } = options;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await loadCheckoutScriptOnce(options);
      return; // 成功加载，退出重试循环
    } catch (error) {
      console.error(`第 ${attempt} 次加载 Checkout.com 脚本失败:`, error);
      
      if (attempt === maxRetries) {
        throw new Error(`经过 ${maxRetries} 次尝试后，Checkout.com 脚本加载失败`);
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
};

/**
 * 初始化 Checkout.com Frames
 */
export const initializeFrames = async (config: FramesConfig): Promise<void> => {
  try {
    // 验证环境变量
    if (!config.publicKey) {
      throw new Error("Checkout.com Public Key 未提供");
    }

    // 确保 Frames 对象存在
    if (!isFramesLoaded()) {
      throw new Error("Checkout.com Frames 对象未找到，请先加载脚本");
    }

    console.log("初始化 Checkout.com Frames，Public Key:", config.publicKey);

    // 默认样式配置
    const defaultStyle = {
      base: {
        color: "#000000",
        fontSize: "15px",
        paddingLeft: "12px",
        fontWeight: "600",
      },
      placeholder: {
        base: {
          color: "rgba(0, 0, 0, .3)",
        },
      },
      focus: {
        border: "2px solid #000000",
        borderRadius: "10px",
      },
    };

    window.Frames.init({
      publicKey: config.publicKey,
      style: { ...defaultStyle, ...config.style },
    });

    console.log("Checkout.com Frames 初始化成功");
  } catch (error) {
    console.error("初始化 Checkout.com Frames 失败:", error);
    throw error;
  }
};

/**
 * 设置 Frames 事件监听器
 */
export const setupFramesEventHandlers = (handlers: {
  onFrameValidationChanged?: (event: any) => void;
  onCardValidationChanged?: (event: any) => void;
  onPaymentMethodChanged?: (event: any) => void;
  onCardTokenizationFailed?: (error: any) => void;
  onCardTokenized?: (event: any) => void;
}) => {
  if (!isFramesLoaded()) {
    throw new Error("Checkout.com Frames 未加载");
  }

  const {
    onFrameValidationChanged,
    onCardValidationChanged,
    onPaymentMethodChanged,
    onCardTokenizationFailed,
    onCardTokenized
  } = handlers;

  // 表单项目更改,form校验
  if (onFrameValidationChanged) {
    window.Frames.addEventHandler(
      window.Frames.Events.FRAME_VALIDATION_CHANGED,
      onFrameValidationChanged
    );
  }

  // 银行卡验证
  if (onCardValidationChanged) {
    window.Frames.addEventHandler(
      window.Frames.Events.CARD_VALIDATION_CHANGED,
      onCardValidationChanged
    );
  }

  // 支付方式更改
  if (onPaymentMethodChanged) {
    window.Frames.addEventHandler(
      window.Frames.Events.PAYMENT_METHOD_CHANGED,
      onPaymentMethodChanged
    );
  }

  // 银行卡token失效
  if (onCardTokenizationFailed) {
    window.Frames.addEventHandler(
      window.Frames.Events.CARD_TOKENIZATION_FAILED,
      onCardTokenizationFailed
    );
  }

  // cardToken
  if (onCardTokenized) {
    window.Frames.addEventHandler(
      window.Frames.Events.CARD_TOKENIZED,
      onCardTokenized
    );
  }
};

/**
 * 提交支付卡片信息
 */
export const submitCard = (cardholderName: string): void => {
  if (!isFramesLoaded()) {
    throw new Error("Checkout.com Frames 未加载");
  }

  if (!cardholderName.trim()) {
    throw new Error("请输入持卡人姓名");
  }

  try {
    window.Frames.enableSubmitForm();
    window.Frames.cardholder = {
      name: cardholderName.trim(),
    };
    window.Frames.submitCard();
  } catch (error) {
    console.error("提交支付信息失败:", error);
    throw new Error("提交支付信息失败");
  }
};
