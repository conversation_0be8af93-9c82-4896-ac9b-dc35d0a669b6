export interface CountryCode {
    code: string
    country: string
    flag: string
    dialCode: string
  }
  
  export const countryCodes: CountryCode[] = [
    // 热门国家/地区 (按使用频率排序)
    {
      code: "CN",
      country: "China",
      flag: "🇨🇳",
      dialCode: "86",
    },
    {
      code: "US",
      country: "United States",
      flag: "🇺🇸",
      dialCode: "1",
    },
    {
      code: "SG",
      country: "Singapore",
      flag: "🇸🇬",
      dialCode: "65",
    },
    {
      code: "HK",
      country: "Hong Kong",
      flag: "🇭🇰",
      dialCode: "852",
    },
    {
      code: "TW",
      country: "Taiwan",
      flag: "🇨🇳",
      dialCode: "886",
    },
    {
      code: "EG",
      country: "Egypt",
      flag: "🇪🇬",
      dialCode: "20",
    },
    {
      code: "MO",
      country: "Macau",
      flag: "🇲🇴",
      dialCode: "853",
    },
    {
      code: "JP",
      country: "Japan",
      flag: "🇯🇵",
      dialCode: "81",
    },
    {
      code: "KR",
      country: "South Korea",
      flag: "🇰🇷",
      dialCode: "82",
    },
    {
      code: "GB",
      country: "United Kingdom",
      flag: "🇬🇧",
      dialCode: "44",
    },
    {
      code: "AU",
      country: "Australia",
      flag: "🇦🇺",
      dialCode: "61",
    },
    {
      code: "CA",
      country: "Canada",
      flag: "🇨🇦",
      dialCode: "1",
    },
    {
      code: "IN",
      country: "India",
      flag: "🇮🇳",
      dialCode: "91",
    },
    {
      code: "DE",
      country: "Germany",
      flag: "🇩🇪",
      dialCode: "49",
    },
    {
      code: "FR",
      country: "France",
      flag: "🇫🇷",
      dialCode: "33",
    },

    // 亚洲国家
    {
      code: "TH",
      country: "Thailand",
      flag: "🇹🇭",
      dialCode: "66",
    },
    {
      code: "MY",
      country: "Malaysia",
      flag: "🇲🇾",
      dialCode: "60",
    },
    {
      code: "ID",
      country: "Indonesia",
      flag: "🇮🇩",
      dialCode: "62",
    },
    {
      code: "PH",
      country: "Philippines",
      flag: "🇵🇭",
      dialCode: "63",
    },
    {
      code: "VN",
      country: "Vietnam",
      flag: "🇻🇳",
      dialCode: "84",
    },
    {
      code: "KH",
      country: "Cambodia",
      flag: "🇰🇭",
      dialCode: "855",
    },
    {
      code: "LA",
      country: "Laos",
      flag: "🇱🇦",
      dialCode: "856",
    },
    {
      code: "MM",
      country: "Myanmar",
      flag: "🇲🇲",
      dialCode: "95",
    },
    {
      code: "BD",
      country: "Bangladesh",
      flag: "🇧🇩",
      dialCode: "880",
    },
    {
      code: "PK",
      country: "Pakistan",
      flag: "🇵🇰",
      dialCode: "92",
    },
    {
      code: "AF",
      country: "Afghanistan",
      flag: "🇦🇫",
      dialCode: "93",
    },
    {
      code: "LK",
      country: "Sri Lanka",
      flag: "🇱🇰",
      dialCode: "94",
    },
    {
      code: "NP",
      country: "Nepal",
      flag: "🇳🇵",
      dialCode: "977",
    },
    {
      code: "MN",
      country: "Mongolia",
      flag: "🇲🇳",
      dialCode: "976",
    },
    {
      code: "KZ",
      country: "Kazakhstan",
      flag: "🇰🇿",
      dialCode: "7",
    },
    {
      code: "UZ",
      country: "Uzbekistan",
      flag: "🇺🇿",
      dialCode: "998",
    },

    // 欧洲国家
    {
      code: "IT",
      country: "Italy",
      flag: "🇮🇹",
      dialCode: "39",
    },
    {
      code: "ES",
      country: "Spain",
      flag: "🇪🇸",
      dialCode: "34",
    },
    {
      code: "NL",
      country: "Netherlands",
      flag: "🇳🇱",
      dialCode: "31",
    },
    {
      code: "BE",
      country: "Belgium",
      flag: "🇧🇪",
      dialCode: "32",
    },
    {
      code: "CH",
      country: "Switzerland",
      flag: "🇨🇭",
      dialCode: "41",
    },
    {
      code: "AT",
      country: "Austria",
      flag: "🇦🇹",
      dialCode: "43",
    },
    {
      code: "SE",
      country: "Sweden",
      flag: "🇸🇪",
      dialCode: "46",
    },
    {
      code: "NO",
      country: "Norway",
      flag: "🇳🇴",
      dialCode: "47",
    },
    {
      code: "DK",
      country: "Denmark",
      flag: "🇩🇰",
      dialCode: "45",
    },
    {
      code: "FI",
      country: "Finland",
      flag: "🇫🇮",
      dialCode: "358",
    },
    {
      code: "PL",
      country: "Poland",
      flag: "🇵🇱",
      dialCode: "48",
    },
    {
      code: "CZ",
      country: "Czech Republic",
      flag: "🇨🇿",
      dialCode: "420",
    },
    {
      code: "HU",
      country: "Hungary",
      flag: "🇭🇺",
      dialCode: "36",
    },
    {
      code: "RO",
      country: "Romania",
      flag: "🇷🇴",
      dialCode: "40",
    },
    {
      code: "BG",
      country: "Bulgaria",
      flag: "🇧🇬",
      dialCode: "359",
    },
    {
      code: "GR",
      country: "Greece",
      flag: "🇬🇷",
      dialCode: "30",
    },
    {
      code: "PT",
      country: "Portugal",
      flag: "🇵🇹",
      dialCode: "351",
    },
    {
      code: "IE",
      country: "Ireland",
      flag: "🇮🇪",
      dialCode: "353",
    },
    {
      code: "IS",
      country: "Iceland",
      flag: "🇮🇸",
      dialCode: "354",
    },
    {
      code: "LU",
      country: "Luxembourg",
      flag: "🇱🇺",
      dialCode: "352",
    },
    {
      code: "MT",
      country: "Malta",
      flag: "🇲🇹",
      dialCode: "356",
    },
    {
      code: "CY",
      country: "Cyprus",
      flag: "🇨🇾",
      dialCode: "357",
    },
    {
      code: "RU",
      country: "Russia",
      flag: "🇷🇺",
      dialCode: "7",
    },
    {
      code: "UA",
      country: "Ukraine",
      flag: "🇺🇦",
      dialCode: "380",
    },
    {
      code: "BY",
      country: "Belarus",
      flag: "🇧🇾",
      dialCode: "375",
    },
    {
      code: "LT",
      country: "Lithuania",
      flag: "🇱🇹",
      dialCode: "370",
    },
    {
      code: "LV",
      country: "Latvia",
      flag: "🇱🇻",
      dialCode: "371",
    },
    {
      code: "EE",
      country: "Estonia",
      flag: "🇪🇪",
      dialCode: "372",
    },
    {
      code: "MD",
      country: "Moldova",
      flag: "🇲🇩",
      dialCode: "373",
    },
    {
      code: "AM",
      country: "Armenia",
      flag: "🇦🇲",
      dialCode: "374",
    },
    {
      code: "GE",
      country: "Georgia",
      flag: "🇬🇪",
      dialCode: "995",
    },
    {
      code: "AZ",
      country: "Azerbaijan",
      flag: "🇦🇿",
      dialCode: "994",
    },

    // 北美洲
    {
      code: "MX",
      country: "Mexico",
      flag: "🇲🇽",
      dialCode: "52",
    },
    {
      code: "GT",
      country: "Guatemala",
      flag: "🇬🇹",
      dialCode: "502",
    },
    {
      code: "BZ",
      country: "Belize",
      flag: "🇧🇿",
      dialCode: "501",
    },
    {
      code: "SV",
      country: "El Salvador",
      flag: "🇸🇻",
      dialCode: "503",
    },
    {
      code: "HN",
      country: "Honduras",
      flag: "🇭🇳",
      dialCode: "504",
    },
    {
      code: "NI",
      country: "Nicaragua",
      flag: "🇳🇮",
      dialCode: "505",
    },
    {
      code: "CR",
      country: "Costa Rica",
      flag: "🇨🇷",
      dialCode: "506",
    },
    {
      code: "PA",
      country: "Panama",
      flag: "🇵🇦",
      dialCode: "507",
    },

    // 南美洲
    {
      code: "BR",
      country: "Brazil",
      flag: "🇧🇷",
      dialCode: "55",
    },
    {
      code: "AR",
      country: "Argentina",
      flag: "🇦🇷",
      dialCode: "54",
    },
    {
      code: "CL",
      country: "Chile",
      flag: "🇨🇱",
      dialCode: "56",
    },
    {
      code: "CO",
      country: "Colombia",
      flag: "🇨🇴",
      dialCode: "57",
    },
    {
      code: "PE",
      country: "Peru",
      flag: "🇵🇪",
      dialCode: "51",
    },
    {
      code: "VE",
      country: "Venezuela",
      flag: "🇻🇪",
      dialCode: "58",
    },
    {
      code: "EC",
      country: "Ecuador",
      flag: "🇪🇨",
      dialCode: "593",
    },
    {
      code: "BO",
      country: "Bolivia",
      flag: "🇧🇴",
      dialCode: "591",
    },
    {
      code: "PY",
      country: "Paraguay",
      flag: "🇵🇾",
      dialCode: "595",
    },
    {
      code: "UY",
      country: "Uruguay",
      flag: "🇺🇾",
      dialCode: "598",
    },
    {
      code: "GY",
      country: "Guyana",
      flag: "🇬🇾",
      dialCode: "592",
    },
    {
      code: "SR",
      country: "Suriname",
      flag: "🇸🇷",
      dialCode: "597",
    },
    {
      code: "GF",
      country: "French Guiana",
      flag: "🇬🇫",
      dialCode: "594",
    }
  ]
  
  export const getCountryByDialCode = (dialCode: string): CountryCode | undefined => {
    return countryCodes.find((country) => country.dialCode === dialCode)
  }

  export const getCountryByCode = (code: string): CountryCode | undefined => {
    return countryCodes.find((country) => country.code.toLowerCase() === code.toLowerCase())
  }

  export const getDefaultCountry = (): CountryCode => {
    return countryCodes.find((country) => country.code === "CN") || countryCodes[0]
  }

  // 搜索国家（支持国家名称、国家代码、电话区号）
  export const searchCountries = (query: string): CountryCode[] => {
    if (!query) return countryCodes

    const searchTerm = query.toLowerCase().trim()

    return countryCodes.filter((country) => {
      return (
        country.country.toLowerCase().includes(searchTerm) ||
        country.code.toLowerCase().includes(searchTerm) ||
        country.dialCode.includes(searchTerm)
      )
    })
  }

  // 获取热门国家（前20个）
  export const getPopularCountries = (): CountryCode[] => {
    return countryCodes.slice(0, 20)
  }

  // 按地区分组国家
  export const getCountriesByRegion = () => {
    const regions = {
      popular: countryCodes.slice(0, 14), // 热门国家/地区
      asia: countryCodes.filter(country =>
        ['TH', 'MY', 'ID', 'PH', 'VN', 'KH', 'LA', 'MM', 'BD', 'PK', 'AF', 'LK', 'NP', 'MN', 'KZ', 'UZ'].includes(country.code)
      ),
      europe: countryCodes.filter(country =>
        ['IT', 'ES', 'NL', 'BE', 'CH', 'AT', 'SE', 'NO', 'DK', 'FI', 'PL', 'CZ', 'HU', 'RO', 'BG', 'GR', 'PT', 'IE', 'IS', 'LU', 'MT', 'CY', 'RU', 'UA', 'BY', 'LT', 'LV', 'EE', 'MD', 'AM', 'GE', 'AZ'].includes(country.code)
      ),
      americas: countryCodes.filter(country =>
        ['MX', 'GT', 'BZ', 'SV', 'HN', 'NI', 'CR', 'PA', 'BR', 'AR', 'CL', 'CO', 'PE', 'VE', 'EC', 'BO', 'PY', 'UY', 'GY', 'SR', 'GF', 'JM', 'CU', 'DO', 'HT', 'TT', 'BB', 'BS', 'PR'].includes(country.code)
      ),
      middleEast: countryCodes.filter(country =>
        ['AE', 'SA', 'QA', 'KW', 'BH', 'OM', 'YE', 'JO', 'LB', 'SY', 'IQ', 'IR', 'AF', 'IL', 'PS', 'TR'].includes(country.code)
      ),
      africa: countryCodes.filter(country =>
        ['EG', 'ZA', 'NG', 'KE', 'GH', 'ET', 'TZ', 'UG', 'RW', 'MA', 'DZ', 'TN', 'LY', 'SD', 'ZW', 'ZM', 'MW', 'MZ', 'BW', 'NA', 'SZ', 'LS'].includes(country.code)
      ),
      oceania: countryCodes.filter(country =>
        ['NZ', 'FJ', 'PG', 'NC', 'VU', 'SB', 'TO', 'WS', 'KI', 'PW', 'FM', 'MH', 'NR', 'TV'].includes(country.code)
      )
    }

    return regions
  }

  // 格式化显示文本
  export const formatCountryDisplay = (country: CountryCode, showFlag: boolean = true): string => {
    const flag = showFlag ? `${country.flag} ` : ''
    return `${flag}${country.country} (+${country.dialCode})`
  }

  // 验证电话号码格式（简单验证）
  export const validatePhoneNumber = (phoneNumber: string, dialCode: string): boolean => {
    // 移除所有非数字字符
    const cleanNumber = phoneNumber.replace(/\D/g, '')

    // 基本长度验证（大多数国家的手机号码在7-15位之间）
    if (cleanNumber.length < 7 || cleanNumber.length > 15) {
      return false
    }

    // 特定国家的验证规则
    switch (dialCode) {
      case '86': // 中国
        return cleanNumber.length === 11 && cleanNumber.startsWith('1')
      case '1': // 美国/加拿大
        return cleanNumber.length === 10
      case '44': // 英国
        return cleanNumber.length >= 10 && cleanNumber.length <= 11
      case '81': // 日本
        return cleanNumber.length >= 10 && cleanNumber.length <= 11
      case '82': // 韩国
        return cleanNumber.length >= 10 && cleanNumber.length <= 11
      case '65': // 新加坡
        return cleanNumber.length === 8
      case '852': // 香港
        return cleanNumber.length === 8
      case '886': // 台湾
        return cleanNumber.length === 9
      case '93': // 阿富汗
        return cleanNumber.length >= 8 && cleanNumber.length <= 9
      default:
        return true // 对于其他国家，只做基本长度验证
    }
  }
  