import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import * as CryptoJS from "crypto-js";
import { string2Uint8Array } from "@/lib/hmacv2";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const isStage = () => {
  return process.env.NODE_ENV === "development";
};

export function aesEncrypt(word: string) {
  const secKey = process.env.NEXT_PUBLIC_AES_KEY;
  if (secKey) {
    console.log("aes key", process.env.NEXT_PUBLIC_AES_KEY);

    const key = CryptoJS.enc.Utf8.parse(secKey);
    // 加密
    const encrypted = CryptoJS.AES.encrypt(word, key, {
      iv: key,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    // 转为 Base64
    const encryptedBase64 = encrypted.toString();
    return encryptedBase64;
  }
  return "";
}
