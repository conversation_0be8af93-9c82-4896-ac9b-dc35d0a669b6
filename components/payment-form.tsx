"use client"

import { useState } from "react"
import { CreditCard, HelpCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { themes, type ThemeKey } from "@/lib/themes"
import { cn } from "@/lib/utils"

interface PaymentFormProps {
  theme: ThemeKey
  totalPrice: string
}

export function PaymentForm({ theme, totalPrice }: PaymentFormProps) {
  const themeConfig = themes[theme]
  const [cardNumber, setCardNumber] = useState("1234 1234 1234 1234")
  const [expiryDate, setExpiryDate] = useState("MM/YY")
  const [securityCode, setSecurityCode] = useState("CVV")
  const [cardholderName, setCardholderName] = useState("Jordan Smith")

  return (
    <div className={cn(themeConfig.cardBg, "rounded-2xl p-6 w-80")}>
      <div className="mb-6">
        <div className={cn("text-sm mb-1", themeConfig.textSecondary)}>Subscribe to Tantan</div>
        <div className={cn("text-2xl font-bold", themeConfig.text)}>{totalPrice}</div>
      </div>

      <div className="space-y-4 mb-6">
        <div className="flex justify-between">
          <span className={themeConfig.textSecondary}>小计</span>
          <span className={themeConfig.text}>US$20.00</span>
        </div>
        <div className="flex justify-between">
          <span className={cn(themeConfig.textSecondary, "flex items-center gap-1")}>
            税 <HelpCircle className="w-3 h-3" />
          </span>
          <span className={themeConfig.textSecondary}>US$0.00</span>
        </div>
        <div className="border-t border-gray-200/20 pt-2">
          <div className="flex justify-between font-semibold">
            <span className={themeConfig.text}>今日应付合计</span>
            <span className={themeConfig.text}>US$20.00</span>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <RadioGroup defaultValue="card" className="flex items-center gap-2">
          <RadioGroupItem value="card" id="card" />
          <Label htmlFor="card" className={cn("flex items-center gap-2", themeConfig.text)}>
            <CreditCard className="w-4 h-4" />
            Card
          </Label>
        </RadioGroup>

        <div>
          <Label className={cn("text-sm", themeConfig.text)}>Cardholder name</Label>
          <Input
            value={cardholderName}
            onChange={(e) => setCardholderName(e.target.value)}
            className={cn("mt-1", themeConfig.cardBg, themeConfig.border, themeConfig.text)}
          />
        </div>

        <div>
          <Label className={cn("text-sm", themeConfig.text)}>Card number</Label>
          <div className="relative">
            <Input
              value={cardNumber}
              onChange={(e) => setCardNumber(e.target.value)}
              className={cn("mt-1 pr-20", themeConfig.cardBg, themeConfig.border, themeConfig.text)}
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-1">
              <div className="w-6 h-4 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">
                V
              </div>
              <div className="w-6 h-4 bg-red-600 rounded"></div>
              <div className="w-6 h-4 bg-blue-500 rounded"></div>
              <div className="w-6 h-4 bg-orange-500 rounded"></div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className={cn("text-sm", themeConfig.text)}>Expiry date</Label>
            <Input
              value={expiryDate}
              onChange={(e) => setExpiryDate(e.target.value)}
              className={cn("mt-1", themeConfig.cardBg, themeConfig.border, themeConfig.text)}
            />
          </div>
          <div>
            <Label className={cn("text-sm flex items-center gap-1", themeConfig.text)}>
              Security code
              <HelpCircle className="w-3 h-3" />
            </Label>
            <Input
              value={securityCode}
              onChange={(e) => setSecurityCode(e.target.value)}
              className={cn("mt-1", themeConfig.cardBg, themeConfig.border, themeConfig.text)}
            />
          </div>
        </div>

        <Button className="w-full bg-black text-white hover:bg-gray-800 py-3 rounded-lg font-semibold">pay</Button>
      </div>
    </div>
  )
}
