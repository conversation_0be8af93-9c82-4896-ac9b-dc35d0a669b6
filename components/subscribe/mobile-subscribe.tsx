"use client"

import { useState } from "react"
import { ArrowLeft, MoreHorizontal, Crown, Check } from "lucide-react"
import { UserAvatar } from "@/components/common/user-avatar"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

const plans = [
  {
    id: "vip",
    months: 12,
    monthlyPrice: "$51.5/mo",
    totalPrice: "$618",
    discount: "16%off",
  },
  {
    id: "see",
    months: 3,
    monthlyPrice: "$76/mo",
    totalPrice: "$228",
  },
  {
    id: "premium",
    months: 3,
    monthlyPrice: "$76/mo",
    totalPrice: "$228",
  },
]

const tabs = [
  { id: "vip", label: "VIP", active: true },
  { id: "see", label: "SEE", active: false },
  { id: "premium", label: "PREMIUM", active: false },
  { id: "ultra", label: "ULTRA PREMIUM", active: false },
]

export function MobileSubscriptionPage() {
  const [selectedPlan, setSelectedPlan] = useState("vip")
  const [activeTab, setActiveTab] = useState("vip")

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile Status Bar */}
      <div className="bg-gray-200 px-4 py-2 flex justify-between items-center text-sm font-medium">
        <span>19:15</span>
        <div className="flex items-center gap-1">
          <div className="flex gap-1">
            <div className="w-1 h-3 bg-black rounded-full"></div>
            <div className="w-1 h-3 bg-black rounded-full"></div>
            <div className="w-1 h-3 bg-black rounded-full"></div>
            <div className="w-1 h-3 bg-gray-400 rounded-full"></div>
          </div>
          <div className="w-4 h-3 border border-black rounded-sm">
            <div className="w-2 h-1 bg-black rounded-sm m-0.5"></div>
          </div>
          <div className="w-6 h-3 border border-black rounded-sm">
            <div className="w-4 h-1 bg-black rounded-sm m-0.5"></div>
          </div>
        </div>
      </div>

      {/* Browser Header */}
      <div className="bg-white px-4 py-3 flex items-center justify-between border-b">
        <ArrowLeft className="w-6 h-6 text-gray-700" />
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-b from-orange-400 to-orange-500 rounded-lg flex items-center justify-center">
            <div className="text-white text-sm">🦊</div>
          </div>
          <div className="text-center">
            <div className="text-sm font-medium">TanTan - More Than Just Asian Dating</div>
            <div className="text-xs text-gray-500">www.int.tantanapp.com</div>
          </div>
        </div>
        <MoreHorizontal className="w-6 h-6 text-gray-700" />
      </div>

      {/* Main Content */}
      <div className="bg-gradient-to-b from-yellow-100 to-yellow-200 min-h-screen">
        {/* User Header */}
        <div className="px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <UserAvatar src="/placeholder.svg?height=40&width=40" alt="Anita" size={40} />
            <div>
              <div className="flex items-center gap-2">
                <span className="font-semibold text-gray-900">Anita</span>
                <span className="bg-yellow-400 text-black text-xs px-2 py-1 rounded-full font-bold">VIP</span>
              </div>
              <div className="text-sm text-gray-600">VIP benefits expire 2028/09/10</div>
            </div>
          </div>
          <button className="text-sm text-gray-600 hover:text-gray-800">Log out</button>
        </div>

        {/* Tab Navigation */}
        <div className="px-4 mb-6">
          <div className="flex gap-2 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors",
                  tab.id === activeTab ? "bg-yellow-400 text-black" : "bg-white/50 text-gray-700 hover:bg-white/70",
                )}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Crown Icon */}
        <div className="flex justify-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-b from-yellow-300 to-yellow-500 rounded-2xl flex items-center justify-center shadow-lg">
            <Crown className="w-10 h-10 text-yellow-800" fill="currentColor" />
          </div>
        </div>

        {/* Subscription Plans */}
        <div className="px-4 mb-6">
          <div className="flex gap-3 overflow-x-auto">
            {plans.map((plan) => (
              <div
                key={plan.id}
                onClick={() => setSelectedPlan(plan.id)}
                className={cn(
                  "flex-shrink-0 w-24 rounded-xl cursor-pointer transition-all duration-200 relative",
                  selectedPlan === plan.id ? "bg-gradient-to-b from-yellow-200 to-yellow-400" : "bg-white",
                )}
              >
                {plan.discount && (
                  <div className="absolute -top-2 left-2 bg-yellow-400 text-black text-xs font-bold px-2 py-1 rounded-full">
                    {plan.discount}
                  </div>
                )}

                {selectedPlan === plan.id ? (
                  <>
                    {/* Selected State - Two sections */}
                    <div className="bg-white mx-1 mt-1 rounded-t-lg p-3 text-center">
                      <div className="text-2xl font-bold text-black">{plan.months}</div>
                      <div className="text-xs text-gray-600">months</div>
                      <div className="text-xs text-orange-500 mt-1">{plan.monthlyPrice}</div>
                    </div>
                    <div className="bg-gradient-to-b from-yellow-300 to-yellow-400 mx-1 mb-1 rounded-b-lg p-3 text-center">
                      <div className="text-lg font-bold text-black">{plan.totalPrice}</div>
                    </div>
                  </>
                ) : (
                  <>
                    {/* Unselected State */}
                    <div className="p-3 text-center">
                      <div className="text-2xl font-bold text-gray-900">{plan.months}</div>
                      <div className="text-xs text-gray-600">months</div>
                      <div className="text-xs text-gray-500 mt-1">{plan.monthlyPrice}</div>
                      <div className="text-lg font-bold text-gray-900 mt-2">{plan.totalPrice}</div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* VIP Privileges */}
        <div className="px-4 mb-8">
          <div className="bg-white rounded-xl p-4">
            <h3 className="text-center text-gray-500 text-sm mb-4">VIP Exclusive Privileges</h3>
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <Check className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Unlimited Likes</div>
                <div className="text-sm text-gray-500">Like Freely Without Worry</div>
              </div>
            </div>
          </div>
        </div>

        {/* Pay Button */}
        <div className="px-4 pb-8">
          <Button className="w-full py-4 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black font-bold text-lg rounded-xl">
            Pay
          </Button>
        </div>

        {/* Mobile Home Indicator */}
        <div className="flex justify-center pb-4">
          <div className="w-32 h-1 bg-black rounded-full"></div>
        </div>
      </div>
    </div>
  )
}
