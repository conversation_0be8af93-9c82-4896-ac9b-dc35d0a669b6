import Image from "next/image"

interface UserAvatarProps {
  src: string
  alt: string
  size?: number
  className?: string
}

export function UserAvatar({ src, alt, size = 48, className = "" }: UserAvatarProps) {
  return (
    <div className={`relative ${className}`}>
      <Image
        src={src || "/placeholder.svg"}
        alt={alt}
        width={size}
        height={size}
        className="rounded-full object-cover"
      />
    </div>
  )
}