/* AlertDialog 居中样式修复 */

/* 确保 AlertDialog 覆盖层正确覆盖整个视口 */
[data-radix-alert-dialog-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 50 !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 确保 AlertDialog 内容始终居中显示 */
[data-radix-alert-dialog-content] {
  position: fixed !important;
  left: 50% !important;
  top: 50% !important;
  z-index: 51 !important;
  transform: translate(-50%, -50%) !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
}

/* 响应式调整 */
@media (max-width: 640px) {
  [data-radix-alert-dialog-content] {
    width: 90vw !important;
    max-width: 90vw !important;
    margin: 0 !important;
  }
}

/* 确保动画效果不影响居中 */
[data-radix-alert-dialog-content][data-state="open"] {
  animation-name: alertDialogContentShow !important;
  animation-duration: 200ms !important;
  animation-timing-function: ease-out !important;
}

[data-radix-alert-dialog-content][data-state="closed"] {
  animation-name: alertDialogContentHide !important;
  animation-duration: 200ms !important;
  animation-timing-function: ease-in !important;
}

@keyframes alertDialogContentShow {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes alertDialogContentHide {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
}

/* 确保在所有浏览器中都能正确显示 */
[data-radix-alert-dialog-content] {
  -webkit-transform: translate(-50%, -50%) !important;
  -moz-transform: translate(-50%, -50%) !important;
  -ms-transform: translate(-50%, -50%) !important;
  -o-transform: translate(-50%, -50%) !important;
}

/* 修复可能的 z-index 问题 */
[data-radix-alert-dialog-overlay],
[data-radix-alert-dialog-content] {
  z-index: 9999 !important;
}

/* 确保内容不会超出视口 */
[data-radix-alert-dialog-content] {
  max-width: min(90vw, 32rem) !important;
  max-height: 90vh !important;
  box-sizing: border-box !important;
}
