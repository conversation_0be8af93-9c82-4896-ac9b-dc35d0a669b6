"use client"

interface SubscriptionPageProps {
  months: number
  monthlyPrice: string
  totalPrice: string
  discount?: string
  onClick?: () => void
}

export function SubscriptionPlanCard({ months, monthlyPrice, totalPrice, discount, onClick }: SubscriptionPageProps) {
  return (
    <div
      className="relative rounded-2xl cursor-pointer transition-all duration-200 border-2 border-yellow-400 shadow-lg scale-105 overflow-hidden"
      onClick={onClick}
    >
      {/* Discount Badge */}
      {discount && (
        <div className="absolute -top-2 left-4 bg-yellow-400 text-black text-xs font-bold px-3 py-1 rounded-full z-10">
          {discount}
        </div>
      )}

      {/* Card Content */}
      <div className="bg-gradient-to-b from-yellow-200 to-yellow-400 rounded-2xl overflow-hidden">
        {/* Top Section - White background */}
        <div className="bg-white mx-2 mt-2 rounded-t-xl p-4 text-center">
          <div className="text-4xl font-bold text-black">{months}</div>
          <div className="text-sm text-gray-600">months</div>
          <div className="text-sm mt-1 text-orange-500">{monthlyPrice}</div>
        </div>

        {/* Bottom Section - Golden background */}
        <div className="bg-gradient-to-b from-yellow-300 to-yellow-400 mx-2 mb-2 rounded-b-xl p-4 text-center">
          <div className="text-2xl font-bold text-black">{totalPrice}</div>
        </div>
      </div>
    </div>
  )
}
