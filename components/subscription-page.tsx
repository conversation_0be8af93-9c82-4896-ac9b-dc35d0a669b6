"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { ChevronLeft, Check, X, LogOut, HelpCircle } from "lucide-react";
import { UserAvatar } from "./user-avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { themeStyles, mobileThemeStyles } from "@/lib/themes";
import { CATEGORY_NAME, tabs } from "@/lib/enums";
import Image from "next/image";
import {
  useCheckoutContractList,
  useCheckoutOrderStatus,
  useGetUserInfo,
  useGetUserPrivilege,
  usePhonePasswordLogin,
} from "@/hooks/queryHook";
import { useCheckoutMerchandises, useCheckoutOrder } from "@/hooks/queryHook";
import { IPhonepassword } from "@/server/login";
import { textUrlMap, TRANSFORM_PRODUCT_NAME } from "@/lib/enums";
import styles from "./subscription-page.module.css";
import { Plan, ShopItem } from "@/types/payment";
import { useRouter } from "next/navigation";
import { getToken } from "@/lib/hmacv2";
import {
  loadCheckoutScript,
  initializeFrames,
  setupFramesEventHandlers,
  submitCard,
  isFramesLoaded,
} from "@/lib/checkout-frames";

import * as AlertDialog from "@radix-ui/react-alert-dialog";

export function SubscriptionPage() {
  const router = useRouter();

  const [selectedPlan, setSelectedPlan] = useState<Plan>({} as Plan);
  const [shopList, setShopList] = useState<ShopItem[]>([]);
  const [planList, setPlanList] = useState<Plan[]>([]);

  const [activeTab, setActiveTab] = useState("vip");
  const [cardNumber, setCardNumber] = useState("1234 1234 1234 1234");
  const [cardholderName, setCardholderName] = useState("");
  const [expiryDate, setExpiryDate] = useState("MM/YY");
  const [securityCode, setSecurityCode] = useState("CVV");
  const [showErrorCardNumber, setShowErrorCardNumber] = useState(false);
  const [showErrorExpiryDate, setShowErrorExpiryDate] = useState(false);
  const [showErrorCvv, setShowErrorCvv] = useState(false);
  const [showErrorCardNotSupport, setShowErrorCardNotSupport] = useState(false);
  const [framesLoading, setFramesLoading] = useState(true);
  const [framesError, setFramesError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [dialogShow, setDialogShow] = useState(false);
  const [jumpUrl, setJumpUrl] = useState("");
  const [dialogTitle, setDialogTitle] = useState("");
  const [dialogDescription, setdialogDescription] = useState("");
  const [dialogBtnText, setDialogBtnText] = useState("Got it");
  const [userInfo, setUserInfo] = useState({} as any);

  const selectedPlanRef = useRef(selectedPlan);
  const isProcessingOrderRef = useRef(false);

  const currentTheme = themeStyles[activeTab as keyof typeof themeStyles];
  const currentMobileTheme =
    mobileThemeStyles[activeTab as keyof typeof mobileThemeStyles];

  const handleTabChange = (key: ShopItem["type"]) => {
    setActiveTab(key);
    const shop = shopList.find((shop) => shop.type === key);
    setPlanList(shop?.plans || []);
    const selectPlan =
      shop?.plans.find((plan) => plan.quantity === shop?.defaultItem) ||
      (shop?.plans[1] as Plan);

    console.log(`selectPlan:`, selectPlan);
    setSelectedPlan(selectPlan);
  };

  const checkoutOrder = useCheckoutOrder();
  const checkoutMerchandises = useCheckoutMerchandises();
  const checkoutOrderStatus = useCheckoutOrderStatus();
  const checkoutContractList = useCheckoutContractList();
  const getUserInfo = useGetUserInfo();
  const getUserPrivilege = useGetUserPrivilege();

  //分类并且更改渠道结构
  const getMerchandiseClassByCategory = (merchandises: any): ShopItem[] => {
    return Object.values(
      merchandises.reduce((acc: any, cur: Plan) => {
        // 更改数据结构
        cur.id = cur?.defaultStockKeepUnit?.id?.toString() ?? "";
        // category ===》 平台 + 支付方式 + 商品名，例：codapay_linepay_vip
        const category = TRANSFORM_PRODUCT_NAME[cur?.productName ?? ""];

        cur.name = category;
        cur.categoryName = category;

        const percent =
          (parseInt(cur.defaultStockKeepUnit.prices.price) /
            (parseInt(cur.defaultStockKeepUnit.prices.unitPrice) *
              cur.quantity)) *
          100;

        cur.discount = percent !== 100 ? percent.toFixed(0) + "%" : "";

        // 过滤掉非规范商品名称/类目名称
        if (!Object.values(TRANSFORM_PRODUCT_NAME).includes(category)) {
          console.warn("lsf1", acc);
          return acc;
        }

        if (!acc[category]) {
          acc[category] = {
            type: category,
            fold: false,
            plans: [cur],
          };
          return acc;
        }
        acc[category].plans.push(cur);
        console.log(acc);
        return acc;
      }, {})
    );
  };

  //获取订阅商品信息
  const handleCheckoutMerchandises = async (): Promise<void> => {
    checkoutMerchandises.mutate(undefined, {
      onSuccess: (data) => {
        const {
          data: {
            merchandises,
            purchaseShowConfig: { merchandiseShowConfig },
          },
        } = data;

        console.log(
          "getMerchandiseClassByCategory:",
          getMerchandiseClassByCategory(merchandises)
        );
        const shopList = getMerchandiseClassByCategory(merchandises);

        shopList.forEach((shopItem) => {
          shopItem.plans.sort((a: any, b: any) => a.quantity - b.quantity);
        });

        const _shopList = shopList.reduce((acc: ShopItem[], cur) => {
          const item = merchandiseShowConfig.find(
            (item: any) =>
              CATEGORY_NAME[item.category as keyof typeof CATEGORY_NAME] ===
              cur.type
          );

          if (!item?.order) {
            return [...acc, cur];
          }

          cur.plans = item?.order?.map((i: any) => {
            return {
              ...cur.plans.find((j: any) => j.quantity === i),
            };
          });

          cur.defaultItem = item.defaultItem;

          return [...acc, cur];
        }, []);
        console.log(`_shopList:`, _shopList);

        setShopList(_shopList);
      },
      onError: (error) => {
        console.log(error);
      },
    });
  };

  const handleGetUserInfo = async (): Promise<void> => {
    getUserInfo.mutate(undefined, {
      onSuccess: (res) => {
        console.log("getUserInfo:", res);

        setUserInfo((res.data.users as any)[0]);
      },
      onError: (error) => {
        console.log(error);
      },
    });
  };

  const handleGetUserPrivilege = async (): Promise<void> => {
    getUserPrivilege.mutate(undefined, {
      onSuccess: (data) => {
        console.log("getUserPrivilege:", data);
      },
      onError: (error) => {
        console.log(error);
      },
    });
  };

  //创建checkout订单
  const handleCheckoutOrder = useCallback(async (data: any) => {
    console.log("🔄 handleCheckoutOrder called with data:", data);

    // 防止重复调用
    if (isProcessingOrderRef.current) {
      console.log(
        "⚠️ Order is already being processed, skipping duplicate call"
      );
      return;
    }

    isProcessingOrderRef.current = true;

    checkoutOrder.mutate(data, {
      onSuccess: (response) => {
        console.log(data);
        console.log("创建订单成功！");
        setJumpUrl(response.data.platformContent);
        setDialogTitle("Payment in progress… ");
        setdialogDescription(
          "Please do not close this window while we process your payment. If you’ve already completed the payment, click below to refresh the status."
        );
        setDialogShow(true);
        handleCheckoutOrderStatus({
          orderIdentifier: response.data.orderIdentifier,
        });

        window.open(response.data.platformContent, "_blank");

        // 重置处理状态
        isProcessingOrderRef.current = false;
      },
      onError: (error) => {
        console.log("订单失败");
        // 重置处理状态
        isProcessingOrderRef.current = false;
      },
    });
  }, []);

  const handleLogOut = () => {
    localStorage.removeItem("token");
    router.push("/login");
  };

  //查询订单状态
  const handleCheckoutOrderStatus = useCallback(
    (data: { orderIdentifier: string }) => {
      checkoutOrderStatus.mutate(data, {
        onSuccess: (data) => {
          setDialogTitle("Payment successful!");
          setdialogDescription(
            "Your payment was successful, and Your new 「VIP(XXX)subscription」 is now active. You can now enjoy all exclusive benefits in the TanTan app."
          );
        },
        onError: (error) => {
          console.log("error:", error);
        },
      });
    },
    []
  );

  // 错误信息管理
  const errorMessage = {
    show: (element: string) => {
      switch (element) {
        case "card-number":
          setShowErrorCardNumber(true);
          break;
        case "expiry-date":
          setShowErrorExpiryDate(true);
          break;
        case "cvv":
          setShowErrorCvv(true);
          break;
        case "card-not-support":
          setShowErrorCardNotSupport(true);
      }
    },
    hide: (element: string) => {
      switch (element) {
        case "card-number":
          setShowErrorCardNumber(false);
          break;
        case "expiry-date":
          setShowErrorExpiryDate(false);
          break;
        case "cvv":
          setShowErrorCvv(false);
          break;
        case "card-not-support":
          setShowErrorCardNotSupport(false);
      }
    },
  };
  // 使用工具函数加载和初始化 Checkout.com Frames

  //初始化checkoutjs
  const initFrames = async (): Promise<void> => {
    try {
      // 验证环境变量
      const publicKey = process.env.NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY;
      if (!publicKey) {
        throw new Error("NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY 环境变量未设置");
      }

      // 使用工具函数初始化 Frames
      await initializeFrames({
        publicKey,
        style: {
          base: {
            color: "#000000",
            fontSize: "15px",
            paddingLeft: "12px",
            fontWeight: "600",
          },
          placeholder: {
            base: {
              color: "rgba(0, 0, 0, .3)",
            },
          },
          focus: {
            border: "2px solid #000000",
            borderRadius: "10px",
          },
        },
      });

      // 设置事件监听器
      setupFramesEventHandlers({
        onFrameValidationChanged: (event: any) => {
          console.log("Frame validation changed:", event);
          const element = event.element;
          if (event.isValid || event.isEmpty) {
            errorMessage.hide(element);
          } else {
            errorMessage.show(element);
          }
        },
        onCardValidationChanged: (valid: any) => {
          console.log("Card validation changed:", valid);
          // 可以在这里添加表单验证逻辑
        },
        onPaymentMethodChanged: (event: any) => {
          console.log("Payment method changed:", event);
          const supportCards = ["mastercard", "visa"];
          if (event && event.paymentMethod) {
            if (supportCards.indexOf(event.paymentMethod.toLowerCase()) >= 0) {
              errorMessage.hide("card-not-support");
            } else {
              errorMessage.show("card-not-support");
            }
          }
        },
        onCardTokenizationFailed: (error: any) => {
          // console.error("Card tokenization failed:", error);
          setFramesError("支付处理失败，请重试");
        },
        onCardTokenized: (event: any) => {
          console.log("🎯 onCardTokenized triggered, token:", event.token);

          // 防止重复调用
          if (isProcessingOrderRef.current) {
            console.log(
              "⚠️ Order is already being processed, ignoring tokenized event"
            );
            return;
          }

          const selectedPlan = selectedPlanRef.current;

          if (!selectedPlan || !selectedPlan.id) {
            console.error("未选择有效的订阅计划");
            setFramesError("未选择有效的订阅计划");
            return;
          }

          handleCheckoutOrder({
            paymentMethod: "card",
            token: event.token,
            cardType: "token",
            currency: selectedPlan?.defaultStockKeepUnit?.prices?.currencyCode,
            ...{
              symbol:
                selectedPlan?.defaultStockKeepUnit?.prices?.currencySymbol,
              type: "sub", // 订阅类固定sub,
              name: `${selectedPlan.quantity}%@ month(s)${selectedPlan.productName}`,
              itemId: selectedPlan.id,
              value: selectedPlan.defaultStockKeepUnit.prices.price,
            },
          });
        },
      });
    } catch (error) {
      console.error("初始化 Checkout.com Frames 失败:", error);
      throw error;
    }
  };

  const handlePayment = () => {
    // 检查 Frames 是否已经初始化
    if (!isFramesLoaded()) {
      console.error("Checkout.com Frames 未初始化");
      setFramesError("支付系统未就绪，请稍后重试");
      return;
    }

    if (framesLoading) {
      console.warn("支付系统正在加载中，请稍候");
      return;
    }

    if (!cardholderName.trim()) {
      console.error("请输入持卡人姓名");
      setFramesError("请输入持卡人姓名");
      return;
    }

    try {
      submitCard(cardholderName);
    } catch (error) {
      console.error("提交支付信息失败:", error);
      setFramesError(
        error instanceof Error ? error.message : "提交支付信息失败，请重试"
      );
    }
  };

  useEffect(() => {
    // 获取商品信息、用户信息、订阅信息
    handleCheckoutMerchandises();
    handleGetUserInfo();
    handleGetUserPrivilege();
    const initializePayment = async () => {
      try {
        setFramesLoading(true);
        setFramesError(null);

        console.log("开始初始化支付系统...");

        // 首先加载 Checkout.com 脚本
        await loadCheckoutScript();

        // 脚本加载成功后初始化 Frames
        await initFrames();

        setFramesLoading(false);
        console.log("支付系统初始化成功");
      } catch (error) {
        console.error("初始化支付系统失败:", error);
        setFramesLoading(false);
        setFramesError(
          error instanceof Error ? error.message : "支付系统初始化失败"
        );
        setRetryCount((prev) => prev + 1);
      }
    };

    initializePayment();
    checkoutContractList.mutate(undefined, {
      onSuccess: (res) => {
        console.log("checkoutContractList:", res.data);
      },
    });
  }, []);

  // 重试函数
  const retryInitialization = () => {
    if (retryCount < 3) {
      setFramesError(null);
      // 重新触发初始化
      const initializePayment = async () => {
        try {
          setFramesLoading(true);
          await loadCheckoutScript({ maxRetries: 2 });
          await initFrames();
          setFramesLoading(false);
          setRetryCount(0); // 重置重试计数
          console.log("重试初始化成功");
        } catch (error) {
          setFramesLoading(false);
          setFramesError(
            error instanceof Error ? error.message : "支付系统初始化失败"
          );
          setRetryCount((prev) => prev + 1);
          console.error("重试初始化失败:", error);
        }
      };
      initializePayment();
    }
  };

  useEffect(() => {
    shopList.length && handleTabChange("vip");
  }, [shopList]);

  useEffect(() => {
    selectedPlanRef.current = selectedPlan;
  }, [selectedPlan.id]);

  return (
    <div className="min-h-screen">
      {/* Mobile Layout - 保持不变 */}
      <div
        className={cn("md:hidden min-h-screen ", currentMobileTheme.background)}
      >
        {/* Mobile Header */}
        <div className="bg-gray-800 px-4 py-4 flex items-center justify-between">
          <ChevronLeft className="w-6 h-6 text-white" strokeWidth={2} />
          <div className="w-12 h-12 bg-gradient-to-b from-orange-400 to-orange-500 rounded-2xl flex items-center justify-center">
            <div className="text-white text-xl ">🦊</div>
          </div>
          <div className="w-6"></div>
        </div>

        {/* User Profile Section */}
        <div className="px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <UserAvatar
                src="/placeholder.svg?height=48&width=48"
                alt="Anita"
                size={48}
              />
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <span
                    className={cn("font-semibold text-lg", currentTheme.text)}
                  >
                    Anita
                  </span>
                  <span className="bg-orange-400 text-white text-xs px-2 py-1 rounded-md ">
                    {activeTab.toUpperCase()}
                  </span>
                </div>
                <div className={cn("text-sm", currentTheme.textSecondary)}>
                  VIP benefits expire 2025/09/10
                </div>
              </div>
            </div>
            <button
              onClick={handleLogOut}
              className={cn(
                "text-sm font-medium px-3 py-2 rounded-lg bg-white/20",
                currentTheme.textSecondary
              )}
            >
              Log out
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-4 pb-8">
          <div className="flex gap-3 bg-[rgba(255,255,255,0.4)]">
            {tabs.map((tab) => (
              <button
                key={tab.type}
                onClick={() => handleTabChange(tab.type)}
                className={cn(
                  "px-4 py-3 rounded-2xl text-sm font-semibold transition-colors flex-1 text-center",
                  tab.label === activeTab
                    ? currentTheme.tabSelected
                    : currentTheme.tabUnselected
                )}
              ></button>
            ))}
          </div>
        </div>

        {/* Icon */}
        <div className="flex justify-center pb-12">
          <div
            className={cn(
              "w-[160px] h-[152px] rounded-3xl flex items-center justify-center ",
              currentMobileTheme.bg
            )}
          >
            <div className="text-4xl">{}</div>
          </div>
        </div>

        {/* Subscription Plans */}
        {/* <div className="px-4 pb-8">
          <div className="flex gap-4 h-[134px] ">
            {plans.map((plan) => (
              <div
                key={plan.id}
                onClick={() => setSelectedPlan(plan.id)}
                className={cn(
                  " p-0.5 flex-1 rounded-2xl cursor-pointer transition-all  relative shadow-md border-2",
                  selectedPlan === plan.id
                    ? cn(
                        currentTheme.selectedCard,
                        currentTheme.selectedCardBorder
                      )
                    : currentTheme.unselectedCard
                )}
              >
                {plan && selectedPlan === plan.id && (
                  <div
                    className={cn(
                      "w-[81px] h-[24px] absolute -top-3 left-3 text-gray-800 text-xs  px-3 py-1 rounded-full shadow-sm flex justify-center ",
                      currentMobileTheme.selectedCard,
                      selectedPlan === plan.id
                        ? currentMobileTheme.planSelectText
                        : currentMobileTheme.planUnSelectText,
                      selectedPlan === plan.id
                        ? currentMobileTheme.selectedCardBorder
                        : currentMobileTheme.unSelectedCardBorder,
                      selectedPlan === plan.id
                        ? currentMobileTheme.selectedCard
                        : currentMobileTheme.unselectedCard
                    )}
                  >
                    {16}
                  </div>
                )}

                {selectedPlan === plan.id ? (
                  <>
                    <div className=" h-[97px] bg-white  rounded-t-xl pt-4 text-center">
                      <div className="text-[32px] leading-[32px]  text-gray-800">
                        {plan.quantity}
                      </div>
                      <div className="text-[13px] leading-[13px] text-gray-500 mb-[7px]">
                        months
                      </div>
                      <div
                        className={cn(
                          "text-[13px] leding-[14px]",
                          currentMobileTheme.planPriceText
                        )}
                      >
                        {plan.defaultStockKeepUnit.prices.unitPrice}
                      </div>
                    </div>
                    <div
                      className={cn(
                        "mx-2  rounded-b-xl  text-center",
                        currentMobileTheme.selectedCard
                      )}
                    >
                      <div
                        className={cn(
                          "text-[16px] mt-1.5 font-medium",
                          currentMobileTheme.text
                        )}
                      >
                        {plan.defaultStockKeepUnit.prices.price}
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="pt-[19px] text-center">
                    <div className="text-[30px] leading-[30px]  text-gray-400">
                      {plan.quantity}
                    </div>
                    <div className="text-[13px] leading-[14px] text-gray-400 mb-2">
                      months
                    </div>
                    <div className="text-[13px] leading-[14px] text-gray-400 font-medium mb-3">
                      {plan.defaultStockKeepUnit.prices.unitPrice}
                    </div>
                    <div className="text-[16px] mt-1.5 font-medium text-gray-400">
                      {plan.defaultStockKeepUnit.prices.price}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div> */}

        {/* Features */}
        <div className="px-4 pb-8">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-center text-gray-400 text-sm mb-6 font-medium">
              {activeTab.toUpperCase()} Exclusive Privileges
            </h3>
            <div className="space-y-4">
              {currentTheme.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                    <Check className="w-5 h-5 text-white" strokeWidth={3} />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-800 text-lg mb-1">
                      {feature.name}
                    </div>
                    <div className="text-sm text-gray-500">{feature.desc}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Pay Button */}
        <div className="px-4 pb-8">
          <Button
            className={cn(
              "w-full py-4  text-lg rounded-2xl shadow-md border-0",
              currentTheme.button
            )}
          >
            Pay
          </Button>
        </div>

        {/* Home Indicator */}
        <div className="flex justify-center pb-6">
          <div className="w-32 h-1 bg-black rounded-full"></div>
        </div>
      </div>

      {/* Desktop Layout  */}
      <div className={cn("hidden md:block min-h-screen bg-black")}>
        {/* 背景图 */}
        <div
          className={cn(
            "opacity-60 hidden md:block min-h-screen bg-[url('https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/desktop-bg.png')] bg-no-repeat bg-[length:100%_100%] "
          )}
        ></div>
        {/* 订阅页面 */}
        <div
          className={cn(
            "overflow-hidden h-[692px] w-[1024px] mx-auto pt-[24px] pl-[20px] pr-[20px] mb-[16px] absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2 rounded-[36px]",
            currentTheme.background
          )}
        >
          {/* ultra背景渐变 */}
          {activeTab === "ultra" && (
            <div className="bg-[url('https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/bg_ultra_linear.png')] absolute -top-[62px] -right-[19px] -z-10 w-[577px] h-[727px] bg-[length:100%_100%] bg-no-repeat"></div>
          )}
          {/* Header */}
          <div
            className={cn(
              "rounded-3xl h-[50px] mb-[25px] flex items-center justify-between",
              currentTheme.headerBg
            )}
          >
            <div className="flex items-center gap-4">
              <UserAvatar
                // src="/placeholder.svg?height=60&width=60"
                src={
                  userInfo.pictures?.[0]?.url ??
                  "https://auto.tancdn.com/v1/images/eyJpZCI6IlgyN1kyU0pSMk1ZSlpGTVNNV05STktQUkZWSzdZRzA2IiwidyI6MTg5LCJoIjoxODYsImQiOjAsIm10IjoiaW1hZ2UvanBlZyIsImRoIjoxMDMzMzA5NjM2MDkwMjEzMTM0fQ?format=originalOFGHLERTH"
                }
                alt="Anita"
                size={60}
              />
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <span
                    className={cn("text-[21px] font-medium", currentTheme.text)}
                  >
                    {userInfo.name}
                  </span>
                  <span className="bg-yellow-400 text-white 800 text-sm px-3 py-1 rounded-full ">
                    VIP
                  </span>
                </div>
                <div
                  className={cn(
                    "text-sm text-gray-700 ",
                    currentTheme.textSecondary
                  )}
                >
                  Your
                  <span className="text-red-400"> VIP </span>
                  benefits will expire on
                  <span className="text-red-400"> 2025/09/10</span>. Renew or
                  upgrade now!
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={handleLogOut}
                className="flex items-center gap-2 bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                Log out
              </button>
              <button
                className={cn("transition-colors", currentTheme.textSecondary)}
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          <div className="flex gap-8">
            {/* Left Content */}
            <div className="flex-1 w-[652px]">
              {/* Tab Navigation */}
              <div
                className={cn(
                  "flex gap-4 mb-[30px] bg-[rgba(255,255,255,0.4)] rounded-[20px] pl-1.5 pr-1.5",
                  currentTheme.tabUnselected
                )}
              >
                {tabs.map((tab) => (
                  <button
                    key={tab.type}
                    onClick={() => handleTabChange(tab.type)}
                    className={cn(
                      "px-6 py-3 rounded-2xl font-semibold transition-colors w-[160px] h-[68px] flex items-center justify-center mt-1.5 mb-1.5 flex-1",
                      tab.type === activeTab
                        ? currentTheme.tabSelected
                        : currentTheme.tabUnselected
                    )}
                  >
                    {tab.type === activeTab ? (
                      <Image
                        alt="Ultra"
                        key={tab.type}
                        height={
                          textUrlMap[tab.type as keyof typeof textUrlMap].height
                        }
                        width={
                          textUrlMap[tab.type as keyof typeof textUrlMap].width
                        }
                        src={
                          textUrlMap[tab.type as keyof typeof textUrlMap]
                            .selected
                        }
                        className=" object-cover"
                      ></Image>
                    ) : (
                      <Image
                        alt="Ultra"
                        layout="auto"
                        key={tab.type}
                        height={
                          textUrlMap[tab.type as keyof typeof textUrlMap].height
                        }
                        width={
                          textUrlMap[tab.type as keyof typeof textUrlMap].width
                        }
                        src={
                          ["vip", "see"].includes(activeTab)
                            ? textUrlMap[tab.type as keyof typeof textUrlMap]
                                .unSelected.light
                            : textUrlMap[tab.type as keyof typeof textUrlMap]
                                .unSelected.dark
                        }
                        className=" object-cover"
                      ></Image>
                    )}
                  </button>
                ))}
              </div>

              {/* Subscription Plans */}
              <div className="grid grid-cols-3 gap-6 mb-4 h-[216px] box-content">
                {planList.map((plan) => (
                  <div
                    key={plan.id}
                    onClick={() => setSelectedPlan({ ...plan })}
                    className={cn(
                      "h-full rounded-xl cursor-pointer transition-all duration-300 relative border-primary border-[4px] ",
                      selectedPlan.id === plan.id
                        ? cn(currentTheme.selectedCardBorder)
                        : cn(
                            currentTheme.unSelectedCardBorder,
                            currentTheme.cardBackground
                          )
                    )}
                  >
                    {plan.discount && selectedPlan.id === plan.id && (
                      <div
                        className={cn(
                          "text-black w-[112.5px] h-[33.33px] absolute -translate-y-1/2 -translate-x-1/2 left-1/2  text-xs  px-3 py-1 rounded-full z-10 flex justify-center  items-center",
                          currentTheme.selectedCard,
                          selectedPlan.id === plan.id
                            ? currentTheme.planSelectText
                            : currentTheme.planUnSelectText,
                          selectedPlan.id === plan.id
                            ? currentTheme.selectedCardBorder
                            : currentTheme.unSelectedCardBorder,
                          selectedPlan.id === plan.id
                            ? currentTheme.selectedCard
                            : currentTheme.unselectedCard
                        )}
                      >
                        {plan.discount}
                      </div>
                    )}

                    {selectedPlan.id === plan.id ? (
                      <>
                        {/* Selected State */}
                        <div
                          className={cn(
                            "p-6 text-center rounded-xl",
                            currentTheme.cardBackground
                          )}
                        >
                          <div
                            className={cn(
                              "text-5xl  text-gray-800 mb-2",
                              currentTheme.text
                            )}
                          >
                            {plan.quantity}
                          </div>
                          <div
                            className={cn(
                              "text-sm text-gray-500 mb-3",
                              currentTheme.textSecondary
                            )}
                          >
                            months
                          </div>
                          <div
                            className={cn(
                              "text-s  font-medium",
                              currentTheme.planPriceText
                            )}
                          >
                            ${plan.defaultStockKeepUnit.prices.unitPrice}/mo
                          </div>
                        </div>
                        <div
                          className={cn(
                            "p-6 text-center h-[70px]",
                            currentTheme.selectedCard
                          )}
                        >
                          <div
                            className={cn(
                              "text-2xl",
                              currentTheme.priceTextSelected
                            )}
                          >
                            ${plan.defaultStockKeepUnit.prices.price}
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="p-6 text-center">
                        <div className={cn("text-5xl mb-2", currentTheme.text)}>
                          {plan.quantity}
                        </div>
                        <div
                          className={cn(
                            "text-sm text-gray-800 mb-3",
                            currentTheme.text
                          )}
                        >
                          months
                        </div>
                        <div
                          className={cn(
                            "text-sm font-medium mb-4",
                            currentTheme.planPriceText
                          )}
                        >
                          ${plan.defaultStockKeepUnit.prices.unitPrice}/mo
                        </div>
                        <div
                          className={cn(
                            "text-2xl text-gray-800",
                            currentTheme.priceTextUnSelected
                          )}
                        >
                          ${plan.defaultStockKeepUnit.prices.price}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Terms */}
              <div
                className={cn(
                  "text-xs mb-2 mt-4 inline-block ",
                  currentTheme.textSecondary
                )}
              >
                Auto-renewable subscription, cancel anytime in App Store
                settings, automatic renewal 24 hours before the subscription
                period ends. By clicking "Continue", you agree to and accept our{" "}
                <span className="text-blue-500 underline cursor-pointer">
                  Terms of Service
                </span>{" "}
                and{" "}
                <span className="text-blue-500 underline cursor-pointer">
                  Privacy Policy
                </span>
                .
              </div>

              {/* Special Feature for SEE */}
              {currentTheme.specialFeature && (
                <div className="mb-8 flex justify-center">
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="relative">
                        {currentTheme.specialFeature.avatars.map(
                          (avatar, index) => (
                            <div
                              key={index}
                              className={cn(
                                "absolute w-16 h-16 rounded-full border-4 border-white overflow-hidden",
                                index === 0 && "left-0 z-30",
                                index === 1 && "left-8 z-20",
                                index === 2 && "left-16 z-10"
                              )}
                            >
                              <img
                                src={avatar || "/placeholder.svg"}
                                alt="User"
                                className="w-full h-full object-cover"
                              />
                            </div>
                          )
                        )}
                        <div className="w-24 h-16"></div>
                        <div className="absolute bottom-0 right-0 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-lg">🦊</span>
                        </div>
                      </div>
                    </div>
                    <h3 className={cn("text-xl  mb-2", currentTheme.text)}>
                      {currentTheme.specialFeature.title}
                    </h3>
                    <p className={cn("text-sm", currentTheme.textSecondary)}>
                      {currentTheme.specialFeature.desc}
                    </p>
                  </div>
                </div>
              )}

              {/* Features Table */}
              <div
                className={cn(
                  "rounded-2xl overflow-scroll max-h-[199px] w-full"
                )}
              >
                {/* <div className="grid grid-cols-4 gap-0">
                
                  <div className="p-4">
                    <span className={cn("font-semibold", currentTheme.text)}>
                      Features
                    </span>
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.featuresHeaderBg,
                      "text-white"
                    )}
                  >
                    VIP
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.text
                    )}
                  >
                    Premium
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.text
                    )}
                  >
                    Ultra Premium
                  </div>

                  {currentTheme.features.map((feature, index) => (
                    <div
                      key={index}
                      className="col-span-4 grid grid-cols-4 gap-0 border-t border-gray-200/20"
                    >
                      <div className="p-4">
                        <div className={cn("font-medium", currentTheme.text)}>
                          {feature.name}
                        </div>
                        <div
                          className={cn("text-sm", currentTheme.textSecondary)}
                        >
                          {feature.desc}
                        </div>
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                    </div>
                  ))}
                </div> */}

                <Image
                  alt="feature"
                  height={1355}
                  width={652}
                  src={
                    "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_feature.png"
                  }
                  className="w-full"
                ></Image>
              </div>
            </div>

            {/* Right Payment Panel */}
            <div className="w-80">
              <div className={cn("rounded-2xl p-4", currentTheme.paymentBg)}>
                <div className="mb-6">
                  <div className="text-sm text-gray-500 mb-1">
                    Subscribe to Tantan
                  </div>
                  <div className="text-2xl text-gray-800">US${selectedPlan.defaultStockKeepUnit?.prices?.price}</div>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between text-sm">
                    {/* <span className="text-gray-500">小计</span>
                    <span className="text-gray-800">US$20.00</span> */}
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 flex items-center gap-1">
                      {/* 税 <HelpCircle className="w-3 h-3" /> */}
                    </span>
                    {/* <span className="text-gray-500">US$0.00</span> */}
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold">
                      {/* <span className="text-gray-800">今日应付合计</span>
                      <span className="text-gray-800">US$20.00</span> */}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* 加载状态提示 */}
                  {framesLoading && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                      <div className="text-blue-600 text-sm">
                        正在加载支付系统...
                      </div>
                    </div>
                  )}

                  {/* 错误状态提示 */}
                  {framesError && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <div className="text-red-600 text-sm mb-2">
                        {framesError}
                      </div>
                      {retryCount < 3 && (
                        <button
                          onClick={retryInitialization}
                          className="text-red-600 text-sm underline hover:no-underline"
                        >
                          重试
                        </button>
                      )}
                    </div>
                  )}

                  <RadioGroup
                    defaultValue="card"
                    className="flex items-center gap-2"
                  >
                    <RadioGroupItem value="card" id="card" />
                    <Label
                      htmlFor="card"
                      className="flex items-center gap-2 text-gray-800"
                    >
                      <div className="w-4 h-4 border border-gray-300 rounded flex items-center justify-center">
                        <div className="w-2 h-2 bg-black rounded-full"></div>
                      </div>
                      Card
                    </Label>
                  </RadioGroup>

                  <div>
                    <Label className="text-sm text-gray-700">
                      Cardholder name
                    </Label>
                    <Input
                      value={cardholderName}
                      onChange={(e) => setCardholderName(e.target.value)}
                      className="mt-1 bg-gray-50 border-gray-200"
                    />
                  </div>

                  <Label className="text-sm text-gray-700">Card number</Label>
                  <div
                    className={cn(
                      "card-number-frame text-sm text-gray-700 h-[31px] mb-[10px] bg-[#F5F5F5] rounded-lg ",
                      styles.cardNumber
                    )}
                  ></div>

                  <div className="flex">
                    <div className="flex-1 mr-[13px]">
                      <Label className="text-sm text-gray-700">
                        Expiry date
                      </Label>
                      <div className="expiry-date-frame text-sm text-gray-700 h-[31px] mb-[10px] bg-[#F5F5F5] rounded-lg "></div>
                    </div>

                    <div className="flex-1">
                      <Label className="text-sm text-gray-700">
                        Security code
                      </Label>
                      <div className="cvv-frame h-[31px] mb-[10px] bg-[#F5F5F5] rounded-lg "></div>
                    </div>
                  </div>

                  {showErrorCardNumber && (
                    <span className="error-message error-message__card-number">
                      Please enter a valid card number
                    </span>
                  )}

                  {showErrorExpiryDate && (
                    <span className="error-message error-message__expiry-date">
                      Please enter a valid expiry date
                    </span>
                  )}

                  {showErrorCvv && (
                    <span
                      className="error-message error-message__cvv"
                      v-show="error_cvv"
                    >
                      Please enter a valid cvv code
                    </span>
                  )}

                  {showErrorCardNotSupport && (
                    <span
                      className="error-message error-message__card-not-support"
                      v-show="error_card_not_support"
                    >
                      This card is temporarily not accepted, please use Visa or
                      MasterCard
                    </span>
                  )}

                  {/* <div className="card-number-frame">
                    <Label className="text-sm text-gray-700">Card number</Label>
                    <div className="relative">
                      <Input
                        value={cardNumber}
                        onChange={(e) => setCardNumber(e.target.value)}
                        className="mt-1 pr-20 bg-gray-50 border-gray-200"
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-1">
                        <div className="w-6 h-4 bg-blue-600 rounded text-white text-xs flex items-center justify-center ">
                          V
                        </div>
                        <div className="w-6 h-4 bg-red-600 rounded"></div>
                        <div className="w-6 h-4 bg-blue-500 rounded"></div>
                        <div className="w-6 h-4 bg-orange-500 rounded"></div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 expiry-date-frame">
                    <div>
                      <Label className="text-sm text-gray-700">
                        Expiry date
                      </Label>
                      <Input
                        value={expiryDate}
                        onChange={(e) => setExpiryDate(e.target.value)}
                        className="mt-1 bg-gray-50 border-gray-200"
                      />
                    </div>
                    <div>
                      <Label className="text-sm text-gray-700 flex items-center gap-1">
                        Security code
                        <HelpCircle className="w-3 h-3" />
                      </Label>
                      <Input
                        value={securityCode}
                        onChange={(e) => setSecurityCode(e.target.value)}
                        className="mt-1 bg-gray-50 border-gray-200 cvv-frame"
                      />
                    </div>
                  </div> */}

                  <Button
                    onClick={() => handlePayment()}
                    disabled={
                      framesLoading || !!framesError || !cardholderName.trim()
                    }
                    className={cn(
                      "w-full py-3 rounded-lg font-semibold transition-colors",
                      framesLoading || !!framesError || !cardholderName.trim()
                        ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                        : "bg-black text-white hover:bg-gray-800"
                    )}
                  >
                    {framesLoading ? "加载中..." : "Pay"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 弹窗提示 */}

      {/* <AlertDialog
        open={dialogShow}
        onOpenChange={() => setDialogShow(!dialogShow)}
      >
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-center text-lg font-semibold">
              Forgot your password?
            </AlertDialogTitle>
            <AlertDialogDescription className="text-center text-gray-600 mt-4">
              <br />
              <br />
              You can reset it in the TanTan app under:
              <br />
              Me → Settings → Account & Security →
              <br />
              Password Management.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex justify-center mt-6">
            <AlertDialogAction
              onClick={() => window.open(jumpUrl, "_blank")}
              className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-2 rounded-lg font-medium"
            >
              Got it
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog> */}

      <AlertDialog.Root open={dialogShow} onOpenChange={setDialogShow}>
        {/* Portal 把内容挂载到 body 上 */}
        <AlertDialog.Portal>
          {/* 背景遮罩 */}
          <AlertDialog.Overlay className="fixed inset-0 bg-black/50 z-40" />

          {/* 内容居中 & 设置 z-index */}
          <AlertDialog.Content
            className="
            fixed z-50
            top-1/2 left-1/2
            -translate-x-1/2 -translate-y-1/2
            bg-white p-6 rounded-[36px] shadow-xl
            w-[560px] h-[308px]
          "
          >
            <X
              onClick={() => setDialogShow(false)}
              className="w-5 h-5 text-gray-500 hover:text-gray-700 cursor-pointer absolute top-6 right-6"
            />
            <AlertDialog.Title className="text-[24px] text-color-[#333333] font-semibold text-center mt-4">
              {dialogTitle}
            </AlertDialog.Title>
            <AlertDialog.Description className="text-[16px] text-gray-400 mt-4 text-center">
              {dialogDescription}
            </AlertDialog.Description>

            <div className="flex justify-end gap-3 mt-8 flex justify-self-center">
              <AlertDialog.Action asChild>
                <button
                  onClick={() => setDialogShow(false)}
                  className="bg-[#FF885B] w-[270px]  h-[64px] text-white px-4 py-2 rounded-[12px] "
                >
                  {dialogBtnText}
                </button>
              </AlertDialog.Action>
            </div>
          </AlertDialog.Content>
        </AlertDialog.Portal>
      </AlertDialog.Root>
    </div>
  );
}
