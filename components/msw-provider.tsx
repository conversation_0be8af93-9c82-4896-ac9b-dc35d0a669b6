// components/msw-provider.tsx
"use client";

import { useEffect, useState } from "react";
import { handlers } from "@/mocks/handler";

export function MSWProvider({ children }: { children: React.ReactNode }) {
  const [mswReady, setMswReady] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    const initMSW = async (): Promise<void> => {
      console.log("🔍 MSW initialization check:", {
        NODE_ENV: process.env.NODE_ENV,
        NEXT_PUBLIC_ENABLE_MOCK: process.env.NEXT_PUBLIC_ENABLE_MOCK,
        isWindow: typeof window !== "undefined",
      });

      if (
        process.env.NODE_ENV == "development" &&
        process.env.NEXT_PUBLIC_ENABLE_MOCK === "true" &&
        typeof window !== "undefined"
      ) {
        console.log(
          `NEXT_PUBLIC_ENABLE_MOCK:`,
          process.env.NEXT_PUBLIC_ENABLE_MOCK
        );
        try {
          console.log("🚀 Starting MSW initialization...");
          const { setupWorker } = await import("msw/browser");
          const worker = setupWorker(...handlers);

          console.log("📋 MSW handlers loaded:", handlers.length);

          await worker.start({
            serviceWorker: { url: "/mockServiceWorker.js" },
            onUnhandledRequest: "bypass",
          });

          console.log("✅ MSW initialized successfully");
        } catch (error) {
          console.error("❌ MSW initialization failed:", error);
        }
      } else {
        console.log("⏭️ MSW initialization skipped");
      }
      setMswReady(true);
    };

    if (isClient) {
      initMSW();
    }
  }, [isClient]);

  if (!isClient) {
    return <>{children}</>;
  }

  if (!mswReady) {
    return <div>Loading MSW...</div>;
  }

  return <>{children}</>;
}
