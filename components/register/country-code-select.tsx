"use client"

import { useState, useRef, useEffect } from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { countryCodes, type CountryCode, getCountryByDialCode } from "@/lib/country-codes"

interface CountryCodeSelectorProps {
  value: string
  onChange: (dialCode: string) => void
  className?: string
}

export function CountryCodeSelector({ value, onChange, className }: CountryCodeSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  const selectedCountry = getCountryByDialCode(value) || countryCodes[0]

  // 过滤国家列表
  const filteredCountries = countryCodes.filter(
    (country) =>
      country.country.toLowerCase().includes(searchTerm.toLowerCase()) || country.dialCode.includes(searchTerm),
  )

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
        setSearchTerm("")
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleSelect = (country: CountryCode) => {
    onChange(country.dialCode)
    setIsOpen(false)
    setSearchTerm("")
  }

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "flex items-center gap-2 px-3 h-full border border-gray-200 rounded-l-lg bg-gray-50 text-gray-700 hover:bg-gray-100 transition-colors min-w-[80px]",
          isOpen && "bg-gray-100 border-orange-500",
          className,
        )}
      >
        <span className="text-lg">{selectedCountry.flag}</span>
        <span className="font-medium">{selectedCountry.dialCode}</span>
        <ChevronDown className={cn("w-4 h-4 transition-transform", isOpen && "rotate-180")} />
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 min-w-[280px]"
        >
          {/* 搜索框 */}
          <div className="p-3 border-b border-gray-100">
            <input
              type="text"
              placeholder="Search country..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:border-orange-500"
              autoFocus
            />
          </div>

          {/* 国家列表 */}
          <div className="max-h-60 overflow-y-auto">
            {filteredCountries.length > 0 ? (
              filteredCountries.map((country) => (
                <button
                  key={country.code}
                  onClick={() => handleSelect(country)}
                  className={cn(
                    "w-full flex items-center justify-between px-4 py-3 hover:bg-gray-50 text-left transition-colors",
                    selectedCountry.code === country.code && "bg-orange-50 text-orange-600",
                  )}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-lg">{country.flag}</span>
                    <span className="text-sm text-gray-900">{country.country}</span>
                  </div>
                  <span className="text-sm font-medium text-gray-600">{country.dialCode}</span>
                </button>
              ))
            ) : (
              <div className="px-4 py-3 text-sm text-gray-500 text-center">No countries found</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
