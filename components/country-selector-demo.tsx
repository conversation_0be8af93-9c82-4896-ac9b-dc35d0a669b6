"use client";

import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  countryCodes,
  getCountriesByRegion,
  searchCountries,
  getPopularCountries,
  formatCountryDisplay,
  validatePhoneNumber,
  getCountryByCode,
  type CountryCode,
} from "@/lib/country-codes";

export function CountrySelectorDemo() {
  const [selectedCountry, setSelectedCountry] = useState<CountryCode>(
    getCountryByCode("CN") || countryCodes[0]
  );
  const [phoneNumber, setPhoneNumber] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"all" | "popular" | "regions">("popular");

  // 搜索和过滤国家
  const filteredCountries = useMemo(() => {
    if (viewMode === "popular") {
      return getPopularCountries();
    }
    if (searchQuery) {
      return searchCountries(searchQuery);
    }
    return countryCodes;
  }, [searchQuery, viewMode]);

  // 按地区分组的国家
  const regionGroups = useMemo(() => getCountriesByRegion(), []);

  // 验证电话号码
  const isPhoneValid = useMemo(() => {
    if (!phoneNumber) return null;
    return validatePhoneNumber(phoneNumber, selectedCountry.dialCode);
  }, [phoneNumber, selectedCountry.dialCode]);

  const handleCountrySelect = (countryCode: string) => {
    const country = getCountryByCode(countryCode);
    if (country) {
      setSelectedCountry(country);
    }
  };

  const renderCountryList = (countries: CountryCode[], title?: string) => (
    <div className="space-y-2">
      {title && <h3 className="font-semibold text-sm text-gray-600">{title}</h3>}
      <div className="grid grid-cols-1 gap-1 max-h-60 overflow-y-auto">
        {countries.map((country) => (
          <Button
            key={country.code}
            variant={selectedCountry.code === country.code ? "default" : "ghost"}
            className="justify-start h-auto p-2 text-left"
            onClick={() => handleCountrySelect(country.code)}
          >
            <span className="text-lg mr-2">{country.flag}</span>
            <div className="flex-1 min-w-0">
              <div className="truncate">{country.country}</div>
              <div className="text-xs text-gray-500">+{country.dialCode}</div>
            </div>
          </Button>
        ))}
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">🌍 国家代码选择器演示</h1>
        <p className="text-gray-600">
          包含全球 {countryCodes.length} 个国家和地区的完整列表
        </p>
      </div>

      {/* 当前选择的国家 */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="font-semibold mb-2">当前选择的国家/地区：</h2>
        <div className="flex items-center space-x-3">
          <span className="text-3xl">{selectedCountry.flag}</span>
          <div>
            <div className="font-medium">{selectedCountry.country}</div>
            <div className="text-sm text-gray-600">
              代码: {selectedCountry.code} | 电话区号: +{selectedCountry.dialCode}
            </div>
          </div>
        </div>
      </div>

      {/* 电话号码输入和验证 */}
      <div className="bg-white border rounded-lg p-4">
        <h2 className="font-semibold mb-3">电话号码验证</h2>
        <div className="flex space-x-2">
          <div className="flex items-center space-x-2 bg-gray-50 px-3 py-2 rounded border">
            <span>{selectedCountry.flag}</span>
            <span className="font-mono">+{selectedCountry.dialCode}</span>
          </div>
          <div className="flex-1">
            <Input
              type="tel"
              placeholder="请输入电话号码"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              className={
                isPhoneValid === false
                  ? "border-red-500"
                  : isPhoneValid === true
                  ? "border-green-500"
                  : ""
              }
            />
          </div>
        </div>
        {phoneNumber && (
          <div className="mt-2 text-sm">
            {isPhoneValid === true && (
              <span className="text-green-600">✅ 电话号码格式正确</span>
            )}
            {isPhoneValid === false && (
              <span className="text-red-600">❌ 电话号码格式不正确</span>
            )}
          </div>
        )}
      </div>

      {/* 视图模式选择 */}
      <div className="flex space-x-2">
        <Button
          variant={viewMode === "popular" ? "default" : "outline"}
          onClick={() => setViewMode("popular")}
        >
          热门国家
        </Button>
        <Button
          variant={viewMode === "all" ? "default" : "outline"}
          onClick={() => setViewMode("all")}
        >
          所有国家
        </Button>
        <Button
          variant={viewMode === "regions" ? "default" : "outline"}
          onClick={() => setViewMode("regions")}
        >
          按地区分组
        </Button>
      </div>

      {/* 搜索框 */}
      {viewMode !== "regions" && (
        <div>
          <Label htmlFor="search">搜索国家（支持国家名称、代码、区号）</Label>
          <Input
            id="search"
            placeholder="例如：China, CN, 86"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="mt-1"
          />
        </div>
      )}

      {/* 国家列表 */}
      <div className="border rounded-lg p-4">
        {viewMode === "regions" ? (
          <div className="space-y-6">
            {Object.entries(regionGroups).map(([regionKey, countries]) => {
              const regionNames = {
                popular: "🔥 热门国家/地区",
                asia: "🌏 亚洲",
                europe: "🇪🇺 欧洲",
                americas: "🌎 美洲",
                middleEast: "🕌 中东",
                africa: "🌍 非洲",
                oceania: "🏝️ 大洋洲",
              };
              return (
                <div key={regionKey}>
                  {renderCountryList(
                    countries,
                    regionNames[regionKey as keyof typeof regionNames]
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div>
            <h2 className="font-semibold mb-3">
              {viewMode === "popular" ? "热门国家/地区" : "所有国家/地区"} 
              ({filteredCountries.length})
            </h2>
            {renderCountryList(filteredCountries)}
          </div>
        )}
      </div>

      {/* 统计信息 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h2 className="font-semibold mb-2">📊 统计信息</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="font-medium">总国家数</div>
            <div className="text-2xl font-bold text-blue-600">{countryCodes.length}</div>
          </div>
          <div>
            <div className="font-medium">热门国家</div>
            <div className="text-2xl font-bold text-green-600">{getPopularCountries().length}</div>
          </div>
          <div>
            <div className="font-medium">亚洲国家</div>
            <div className="text-2xl font-bold text-orange-600">{regionGroups.asia.length}</div>
          </div>
          <div>
            <div className="font-medium">欧洲国家</div>
            <div className="text-2xl font-bold text-purple-600">{regionGroups.europe.length}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
