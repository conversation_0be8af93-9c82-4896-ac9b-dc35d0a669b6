"use client";

import { useState, useEffect } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { MSWProvider } from "./msw-provider";
import { AuthQueryProvider } from "./auth-query-provider";

interface ClientProvidersProps {
  children: React.ReactNode;
}

export function ClientProviders({ children }: ClientProvidersProps) {
  const [isClient, setIsClient] = useState(false);

  // 创建一个基础的QueryClient用于SSR
  const basicQueryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: 2,
        staleTime: 1000 * 60 * 5,
        refetchOnWindowFocus: false,
      },
      mutations: {
        retry: 1,
      },
    },
  });

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <AuthQueryProvider>
      <MSWProvider>{children}</MSWProvider>
    </AuthQueryProvider>
  );
}
