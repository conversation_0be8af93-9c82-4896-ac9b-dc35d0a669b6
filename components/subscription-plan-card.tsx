"use client";
import { themes, type ThemeKey } from "@/lib/themes";
import { cn } from "@/lib/utils";

interface SubscriptionPlanCardProps {
  title: string;
  months: number;
  monthlyPrice: string;
  totalPrice: string;
  discount?: string;
  isSelected?: boolean;
  isPopular?: boolean;
  theme: ThemeKey;
  onClick: () => void;
}

export function SubscriptionPlanCard({
  title,
  months,
  monthlyPrice,
  totalPrice,
  discount,
  isSelected,
  isPopular,
  theme,
  onClick,
}: SubscriptionPlanCardProps) {
  const themeConfig = themes[theme];

  return (
    <div
      className={cn(
        "relative rounded-2xl p-0 cursor-pointer transition-all duration-200 border-2  bg-[#fefdf7] ",
        isSelected
          ? "border-yellow-400 shadow-lg scale-105"
          : [themeConfig.border, "hover:scale-102"]
      )}
      onClick={onClick}
    >
      {/* Discount Badge */}
      {discount && (
        <div
          className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 
    bg-yellow-400 text-black  px-3 py-1 rounded-full z-10 
    w-[112.5px] h-[33.33px] flex items-center justify-center text-s"
        >
          {discount}
        </div>
      )}

      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-2 right-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full z-10">
          ULTRA PREMIUM
        </div>
      )}

      {/* Card Content */}
      <div
        className={cn(
          "rounded-2xl overflow-hidden",
          isSelected
            ? "bg-gradient-to-b from-[#FFDC7A] to-[#FFE895]"
            : themeConfig.planCard
        )}
      >
        {/* Top Section - White background for selected state */}
        <div
          className={cn(
            "p-4 text-center",
            isSelected ? "bg-white mx-2 mt-2 rounded-t-xl" : ""
          )}
        >
          <div
            className={cn(
              "text-4xl font-bold",
              isSelected
                ? "text-black"
                : isSelected
                ? themeConfig.selectedPlanText
                : themeConfig.text
            )}
          >
            {months}
          </div>
          <div
            className={cn(
              "text-sm",
              isSelected
                ? "text-gray-600"
                : isSelected
                ? theme === "dark"
                  ? "text-white/70"
                  : "text-black/70"
                : themeConfig.textSecondary
            )}
          >
            months
          </div>
          <div
            className={cn(
              "text-sm mt-1",
              isSelected
                ? "text-orange-500"
                : isSelected
                ? theme === "dark"
                  ? "text-white/70"
                  : "text-black/70"
                : themeConfig.accent
            )}
          >
            {monthlyPrice}
          </div>
        </div>

        {/* Bottom Section - Golden background for selected state */}
        <div className={cn("p-4 text-center w-full", isSelected ? "" : "")}>
          <div
            className={cn(
              "text-2xl font-bold",
              isSelected
                ? "text-black"
                : isSelected
                ? themeConfig.selectedPlanText
                : themeConfig.text
            )}
          >
            {totalPrice}
          </div>
        </div>
      </div>
    </div>
  );
}
